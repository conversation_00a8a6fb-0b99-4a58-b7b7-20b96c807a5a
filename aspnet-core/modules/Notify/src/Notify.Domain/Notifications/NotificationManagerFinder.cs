using Microsoft.Extensions.Options;
using Notify.Options;
using Volo.Abp.DependencyInjection;

namespace Notify.Notifications;

public class NotificationManagerFinder : INotificationManagerFinder, ITransientDependency
{
    private readonly IAbpLazyServiceProvider _lazyServiceProvider;
    private readonly NotifyOptions _options;

    public NotificationManagerFinder(IAbpLazyServiceProvider lazyServiceProvider, IOptions<NotifyOptions> options)
    {
        _lazyServiceProvider = lazyServiceProvider;
        _options = options.Value;
    }
    
    public INotificationManager Find(string notificationMethod)
    {
        var managerType = _options.Providers[notificationMethod].NotificationManagerType;
        return (INotificationManager)_lazyServiceProvider.LazyGetRequiredService(managerType);
    }
}