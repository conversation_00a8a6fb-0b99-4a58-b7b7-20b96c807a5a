using Microsoft.EntityFrameworkCore;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Notify.EntityFrameworkCore;

public static class NotifyDbContextModelCreatingExtensions
{
    public static void ConfigureNotify(
        this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        /* Configure all entities here. Example:

        builder.Entity<Question>(b =>
        {
            //Configure table & schema name
            b.ToTable(NotifyDbProperties.DbTablePrefix + "Questions", NotifyDbProperties.DbSchema);

            b.ConfigureByConvention();

            //Properties
            b.Property(q => q.Title).IsRequired().HasMaxLength(QuestionConsts.MaxTitleLength);

            //Relations
            b.HasMany(question => question.Tags).WithOne().HasForeignKey(qt => qt.QuestionId);

            //Indexes
            b.HasIndex(q => q.CreationTime);
        });
        */

        builder.Entity<UserNotification>(b =>
        {
            b.ToTable(NotifyConsts.NotifyDbProperties.DbTablePrefix + "UserNotifications",
                NotifyConsts.NotifyDbProperties.DbSchema);
            b.ConfigureByConvention();
        });


        builder.Entity<Notification>(b =>
        {
            b.ToTable(NotifyConsts.NotifyDbProperties.DbTablePrefix + "Notifications",
                NotifyConsts.NotifyDbProperties.DbSchema);
            b.ConfigureByConvention();
        });
    }
}