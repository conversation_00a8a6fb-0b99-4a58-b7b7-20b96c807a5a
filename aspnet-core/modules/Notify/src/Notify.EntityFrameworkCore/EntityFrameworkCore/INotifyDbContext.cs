using Microsoft.EntityFrameworkCore;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;

namespace Notify.EntityFrameworkCore;

[ConnectionStringName(NotifyConsts.NotifyDbProperties.ConnectionStringName)]
public interface INotifyDbContext : IEfCoreDbContext
{
    DbSet<UserNotification> UserNotifications { get; set; }
    DbSet<Notification> Notifications { get; set; }
}
