using Notify.Notifications;

namespace Notify.Provider.FCM;

public static class NotificationExtensions
{
    public static void SetFCMTitle(this Notification notification, string text)
    {
        notification.SetDataValue(NotifyProviderFCMConsts.NotificationTitlePropertyName, text);
    }

    public static void SetFCMBody(this Notification notification, string text)
    {
        notification.SetDataValue(NotifyProviderFCMConsts.NotificationBodyPropertyName, text);
    }

    public static string GetFCMTitle(this Notification notification)
    {
        if (notification.GetDataValue(NotifyProviderFCMConsts.NotificationTitlePropertyName) is not string title)
            throw new NotImplementedException();

        return title;
    }

    public static string GetFCMBody(this Notification notification)
    {
        if (notification.GetDataValue(NotifyProviderFCMConsts.NotificationBodyPropertyName) is not string body)
            throw new NotImplementedException();

        return body;
    }
}
