using Volo.Abp.DependencyInjection;

namespace Notify.Provider.FCM;

public class UserDeviceTokenProvider : IUserDeviceTokenProvider, ITransientDependency
{
    private readonly IExternalUserDeviceTokenLookupServiceProvider _externalUserDeviceTokenLookupServiceProvider;

    public UserDeviceTokenProvider(IExternalUserDeviceTokenLookupServiceProvider externalUserDeviceTokenLookupServiceProvider)
    {
        _externalUserDeviceTokenLookupServiceProvider = externalUserDeviceTokenLookupServiceProvider;
    }

    public virtual async Task<List<string>> GetListAsync(Guid userId)
    {
        var tokens = await _externalUserDeviceTokenLookupServiceProvider.GetTokensByUserId(userId);
        if (tokens.IsNullOrEmpty())
            return [];

        return tokens;
    }
}
