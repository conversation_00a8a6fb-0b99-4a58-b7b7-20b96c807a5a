using Volo.Abp.Data;

namespace Notify.Provider.Email;

public static class CreateNotificationInfoModelExtensions
{
    public static void SetSubject(this CreateNotificationModel model, string subject)
    {
        model.SetProperty(NotifyProviderEmailConsts.NotificationSubjectPropertyName, subject);
    }

    public static void SetBody(this CreateNotificationModel model, string body)
    {
        model.SetProperty(NotifyProviderEmailConsts.NotificationBodyPropertyName, body);
    }

    public static string GetSubject(this CreateNotificationModel model)
    {
        var subject = (string?)model.GetProperty(NotifyProviderEmailConsts.NotificationSubjectPropertyName);

        if (subject is null)
            throw new NotImplementedException();

        return subject;
    }

    public static string GetBody(this CreateNotificationModel model)
    {
        var body = (string?)model.GetProperty(NotifyProviderEmailConsts.NotificationBodyPropertyName);

        if (body is null)
            throw new NotImplementedException();

        return body;
    }
}
