using Notify.Notifications.UserNotifications;
using Notify.Notifications;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus;

namespace Notify.Provider.Email;

public class EmailNotificationHandler : ILocalEventHandler<CreateEmailNotificationEto>, ITransientDependency
{
    private readonly INotificationRepository _notificationRepository;
    private readonly IUserNotificationRepository _userNotificationRepository;
    private readonly EmailNotificationManager _emailNotificationManager;

    public EmailNotificationHandler(
        INotificationRepository notificationRepository,
        IUserNotificationRepository userNotificationRepository,
        EmailNotificationManager emailNotificationManager)
    {
        _notificationRepository = notificationRepository;
        _userNotificationRepository = userNotificationRepository;
        _emailNotificationManager = emailNotificationManager;
    }

    public async Task HandleEventAsync(CreateEmailNotificationEto eventData)
    {
        var result = await _emailNotificationManager.CreateAsync(eventData);

        await _notificationRepository.InsertAsync(result.Notification, true);
        
        await _userNotificationRepository.InsertManyAsync(result.UserNotifications, true);
    }
}

