using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp.ExceptionHandling;
using Volo.Abp.Sms;
using Volo.Abp.Uow;

namespace Notify.Provider.Sms;

public class SmsUserNotificationManager : NotificationManagerBase
{
    protected override string NotificationMethod => NotifyProviderSmsConsts.NotificationMethodSms;

    private ISmsSender SmsSender => LazyServiceProvider.LazyGetRequiredService<ISmsSender>();

    private IUserPhoneNumberProvider UserPhoneNumberProvider =>
        LazyServiceProvider.LazyGetRequiredService<IUserPhoneNumberProvider>();


    [UnitOfWork(true)]
    public override async Task<(List<UserNotification> UserNotifications, Notification Notification)>
        CreateAsync(CreateNotificationModel model)
    {
        var notification = new Notification(GuidGenerator.Create());

        notification.SetSmsData(model.GetText());

        var userNotifications = await base.CreateUserNotificationsAsync(notification, model);

        return (userNotifications, notification);
    }

    [UnitOfWork]
    public override async Task SendUserNotificationAsync(UserNotification userNotification,
        Notification notification)
    {
        var userPhoneNumber = await UserPhoneNumberProvider.GetAsync(userNotification.UserId);

        if (userPhoneNumber.IsNullOrWhiteSpace())
        {
            userNotification.SetResult(Clock, false, NotifyProviderSmsConsts.FailureReasonWhenPhoneNumberNotFound);
            await UserNotificationRepository.UpdateAsync(userNotification);
            return;
        }

        if (userPhoneNumber.StartsWith("00"))
            userPhoneNumber = userPhoneNumber[2..];

        var smsMessage = new SmsMessage(userPhoneNumber, notification.GetSmsText());

        try
        {
            await SmsSender.SendAsync(smsMessage);
            userNotification.SetResult(Clock, true);

            await UserNotificationRepository.UpdateAsync(userNotification);
        }
        catch (Exception e)
        {
            Logger.LogException(e);

            var message = e is IHasErrorCode b ? b.Code ?? e.Message : e.ToString();
            userNotification.SetResult(Clock, false, message);

            await UserNotificationRepository.UpdateAsync(userNotification);
        }
    }
}