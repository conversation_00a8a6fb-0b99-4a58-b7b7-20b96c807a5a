using System;
using System.Collections.Generic;

namespace Notify.Provider.Sms;

[Serializable]
public class CreateSmsNotificationEto : CreateNotificationModel
{
    public string Text
    {
        get => this.GetText();
        set => this.SetText(value);
    }

    public CreateSmsNotificationEto(IEnumerable<Guid> userIds, string text) :
        base(NotifyProviderSmsConsts.NotificationMethodSms, userIds)
    {
        Text = text;
    }


    public CreateSmsNotificationEto(Guid userId, string text) :
        base(NotifyProviderSmsConsts.NotificationMethodSms, userId)
    {
        Text = text;
    }
}