<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Notify</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.4" />
    <ProjectReference Include="..\..\src\Notify.EntityFrameworkCore\Notify.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\Notify.Application.Tests\Notify.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="8.3.0" />

  </ItemGroup>

</Project>
