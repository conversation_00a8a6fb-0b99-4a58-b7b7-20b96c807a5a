using System.Threading.Tasks;
using GoTrack.Settings.DTOs;
using Volo.Abp.Application.Services;

namespace GoTrack.Settings;

public interface IGoTrackSettingsAppService : IApplicationService
{
    Task<TrackAccountSubscriptionExpireReminderDto> GetTrackAccountSubscriptionExpireReminderAsync();
    Task UpdateTrackAccountSubscriptionExpireReminderAsync(UpdateTrackAccountSubscriptionExpireReminderDto input);

    Task<SmsBundleExpireReminderDto> GetSmsBundleExpireReminderAsync();
    Task UpdateSmsBundleExpireReminderAsync(UpdateSmsBundleExpireReminderDto input);
}
