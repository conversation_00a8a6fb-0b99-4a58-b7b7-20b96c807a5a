<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>GoTrack</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoTrack.Domain.Shared\GoTrack.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.ObjectExtending" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="8.3.0" />
  </ItemGroup>

</Project>
