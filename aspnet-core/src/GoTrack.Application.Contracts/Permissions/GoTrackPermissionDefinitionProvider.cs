using GoTrack.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace GoTrack.Permissions;

public class GoTrackPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(GoTrackPermissions.GroupName);
        //Define your own permissions here. Example:
        //myGroup.AddPermission(GoTrackPermissions.MyPermission1, L("Permission:MyPermission1"));

        var businessAccountSubscriptionRequestGroup =
            context.AddGroup(GoTrackPermissions.BusinessAccountSubscriptionRequestGroupName, L("PermissionsGroup:BusinessAccountSubscriptionRequestGroup"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestIndex, L("Permissions:" + nameof(GoTrackPermissions.BusinessAccountSubscriptionRequestIndex)));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestDetails, L("Permissions:BusinessAccountSubscriptionRequestDetails"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestStartProcessing, L("Permissions:BusinessAccountSubscriptionRequestStartProcessing"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestFinishProcessing, L("Permissions:BusinessAccountSubscriptionRequestInstallDevices"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestInstallDevices, L("Permissions:BusinessAccountSubscriptionRequestFinishProcessing"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestReject, L("Permissions:BusinessAccountSubscriptionRequestReject"));
        businessAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.BusinessAccountSubscriptionRequestApplyDiscount, L("Permissions:BusinessAccountSubscriptionRequestApplyDiscount"));

        var personalAccountSubscriptionRequestGroup =
            context.AddGroup(GoTrackPermissions.PersonalAccountSubscriptionRequestGroupName, L("PermissionsGroup:PersonalAccountSubscriptionRequestGroup"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestIndex, L("Permissions:" + nameof(GoTrackPermissions.PersonalAccountSubscriptionRequestIndex)));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestDetails, L("Permissions:PersonalAccountSubscriptionRequestDetails"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestStartProcessing, L("Permissions:PersonalAccountSubscriptionRequestStartProcessing"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestInstallDevices, L("Permissions:PersonalAccountSubscriptionRequestInstallDevices"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestFinishProcessing, L("Permissions:PersonalAccountSubscriptionRequestFinishProcessing"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestReject, L("Permissions:PersonalAccountSubscriptionRequestReject"));
        personalAccountSubscriptionRequestGroup.AddPermission(GoTrackPermissions.PersonalAccountSubscriptionRequestApplyDiscount, L("Permissions:PersonalAccountSubscriptionRequestApplyDiscount"));

        var requestGroup =
            context.AddGroup(GoTrackPermissions.RequestGroupName, L("PermissionsGroup:RequestGroup"));
        requestGroup.AddPermission(GoTrackPermissions.RequestIndex, L("Permissions:RequestIndex"));
        requestGroup.AddPermission(GoTrackPermissions.RequestNoteIndex, L("Permissions:RequestNoteIndex"));
        requestGroup.AddPermission(GoTrackPermissions.RequestPayWithCash, L("Permissions:RequestPayWithCash"));

        var deviceGroup =
            context.AddGroup(GoTrackPermissions.DeviceGroupName, L("PermissionsGroup:DeviceGroup"));
        deviceGroup.AddPermission(GoTrackPermissions.DeviceIndex, L("Permissions:DeviceIndex"));
        deviceGroup.AddPermission(GoTrackPermissions.DeviceCreate, L("Permissions:DeviceCreate"));
        deviceGroup.AddPermission(GoTrackPermissions.DeviceDetails, L("Permissions:DeviceDetails"));
        deviceGroup.AddPermission(GoTrackPermissions.GetDeactivatedDevicesByUser, L("Permissions:GetDeactivatedDevicesByUser"));
        deviceGroup.AddPermission(GoTrackPermissions.DeviceTrackAccountInfo, L("Permissions:DeviceTrackAccountInfo"));


        var trackAccountGroup = context.AddGroup(GoTrackPermissions.TrackAccountGroupName, L("PermissionsGroup:TrackAccountGroup"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountIndex, L("Permissions:TrackAccountIndex"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountFeaturesDetails, L("Permissions:TrackAccountFeaturesDetails"));
        trackAccountGroup.AddPermission(GoTrackPermissions.SetFeatureForTrackAccount, L("Permissions:SetFeatureForTrackAccount"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountDetails, L("Permissions:TrackAccountDetails"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountUserAssociations, L("Permissions:TrackAccountUserAssociations"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountSubscriptions, L("Permissions:TrackAccountSubscriptions"));
        trackAccountGroup.AddPermission(GoTrackPermissions.TrackAccountVehiclesWithDevices, L("Permissions:TrackAccountVehiclesWithDevices"));

        var featureGroup = context.AddGroup(GoTrackPermissions.FeatureGroupName, L("PermissionsGroup:FeatureGroup"));
        featureGroup.AddPermission(GoTrackPermissions.FeatureIndex, L("Permissions:FeatureIndex"));

        var alertLogGroup = context.AddGroup(GoTrackPermissions.AlertLogGroupName, L("PermissionsGroup:AlertLogGroupName"));
        alertLogGroup.AddPermission(GoTrackPermissions.AlertLogIndex, L("Permissions:AlertLogIndex"));

        var smsBundleGroup =
            context.AddGroup(GoTrackPermissions.SmsBundleGroupName, L("Permissions:SmsBundleGroupName"));
        smsBundleGroup.AddPermission(GoTrackPermissions.SmsBundleIndex, L("Permissions:SmsBundleIndex"));
        smsBundleGroup.AddPermission(GoTrackPermissions.SmsBundleCreate, L("Permissions:SmsBundleCreate"));
        smsBundleGroup.AddPermission(GoTrackPermissions.SmsBundleDetails, L("Permissions:SmsBundleDetails"));
        smsBundleGroup.AddPermission(GoTrackPermissions.SmsBundleUpdate, L("Permissions:SmsBundleUpdate"));
        smsBundleGroup.AddPermission(GoTrackPermissions.SmsBundleDelete, L("Permissions:SmsBundleDelete"));


        var increaseUserCountRequestGroup =
            context.AddGroup(GoTrackPermissions.IncreaseUserCountRequestGroupName, L("PermissionsGroup:IncreaseUserCountRequestGroup"));
        increaseUserCountRequestGroup.AddPermission(GoTrackPermissions.IncreaseUserCountRequestIndex, L("Permissions:IncreaseUserCountRequestIndex"));
        increaseUserCountRequestGroup.AddPermission(GoTrackPermissions.IncreaseUserCountRequestDetails, L("Permissions:IncreaseUserCountRequestDetails"));


        var renewSubscriptionRequestGroup =
            context.AddGroup(GoTrackPermissions.RenewSubscriptionRequestGroupName, L("PermissionsGroup:RenewSubscriptionRequestGroup"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestAcceptRequest, L("Permissions:RenewSubscriptionRequestAcceptRequest"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestIndex, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestIndex)));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestDetails, L("Permissions:RenewSubscriptionRequestDetails"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestFinishProcessing, L("Permissions:RenewSubscriptionRequestInstallDevices"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestInstallDevices, L("Permissions:RenewSubscriptionRequestFinishProcessing"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestReject, L("Permissions:RenewSubscriptionRequestReject"));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestRemovedVehicles, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestRemovedVehicles)));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestNewVehicles, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestNewVehicles)));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestRemovedUsers, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestRemovedUsers)));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestRemainingVehicles, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestRemainingVehicles)));
        renewSubscriptionRequestGroup.AddPermission(GoTrackPermissions.RenewSubscriptionRequestGetListNewVehiclesWithSearch, L("Permissions:" + nameof(GoTrackPermissions.RenewSubscriptionRequestGetListNewVehiclesWithSearch)));


        var renewSmsPackageRequestGroup =
            context.AddGroup(GoTrackPermissions.RenewSMSPackageRequestGroupName, L("PermissionsGroup:RenewSMSPackageRequestGroupName"));
        renewSmsPackageRequestGroup.AddPermission(GoTrackPermissions.RenewSMSPackageRequestIndex, L("Permissions:RenewSMSPackageRequestIndex"));


        var AddVehiclesRequestGroup =
            context.AddGroup(GoTrackPermissions.AddVehiclesRequestGroupName, L("Permissions:AddVehiclesRequestGroupName"));
        AddVehiclesRequestGroup.AddPermission(GoTrackPermissions.AddVehiclesRequestIndex, L("Permissions:AddVehiclesRequestIndex"));
        AddVehiclesRequestGroup.AddPermission(GoTrackPermissions.AddVehiclesRequestDetails, L("Permissions:AddVehiclesRequestDetails"));
        AddVehiclesRequestGroup.AddPermission(GoTrackPermissions.AddVehiclesRequestInstallDevices, L("Permissions:AddVehiclesRequestInstallDevices"));
        AddVehiclesRequestGroup.AddPermission(GoTrackPermissions.AddVehiclesRequestFinishProcessing, L("Permissions:AddVehiclesRequestFinishProcessing"));
        AddVehiclesRequestGroup.AddPermission(GoTrackPermissions.AddVehiclesRequestGetListVehiclesWithSearch, L("Permissions:AddVehiclesRequestManagementIndexAndSearchVehicles"));

        var pricingGroup = context.AddGroup(GoTrackPermissions.PricingManagementGroupName, L("PermissionsGroup:PricingManagement"));
        pricingGroup.AddPermission(GoTrackPermissions.PricingIndex, L("Permissions:PricingIndex"));
        pricingGroup.AddPermission(GoTrackPermissions.PricingSet, L("Permissions:PricingSet"));

        var subscriptionDurationDiscountGroup =
            context.AddGroup(GoTrackPermissions.SubscriptionDurationDiscountGroupName, L("PermissionsGroup:" + nameof(GoTrackPermissions.SubscriptionDurationDiscountGroupName)));
        subscriptionDurationDiscountGroup.AddPermission(GoTrackPermissions.SubscriptionDurationDiscountIndex, L("Permissions:" + nameof(GoTrackPermissions.SubscriptionDurationDiscountIndex)));
        subscriptionDurationDiscountGroup.AddPermission(GoTrackPermissions.SubscriptionDurationDiscountCreate, L("Permissions:" + nameof(GoTrackPermissions.SubscriptionDurationDiscountCreate)));
        subscriptionDurationDiscountGroup.AddPermission(GoTrackPermissions.SubscriptionDurationDiscountDelete, L("Permissions:" + nameof(GoTrackPermissions.SubscriptionDurationDiscountDelete)));
        
        
        var discountGroup =
            context.AddGroup(GoTrackPermissions.DiscountGroupName, L("PermissionsGroup:" + nameof(GoTrackPermissions.DiscountGroupName)));
        discountGroup.AddPermission(GoTrackPermissions.DiscountIndex, L("Permissions:" + nameof(GoTrackPermissions.DiscountIndex)));
        discountGroup.AddPermission(GoTrackPermissions.DiscountCreate, L("Permissions:" + nameof(GoTrackPermissions.DiscountCreate)));
        discountGroup.AddPermission(GoTrackPermissions.DiscountDelete, L("Permissions:" + nameof(GoTrackPermissions.DiscountDelete)));

        var promoCodeGroup = context.AddGroup(GoTrackPermissions.PromoCodeGroupName, L("PermissionsGroup:PromoCode"));
        promoCodeGroup.AddPermission(GoTrackPermissions.PromoCodeCreate, L("Permissions:PromoCodeCreate"));
        promoCodeGroup.AddPermission(GoTrackPermissions.PromoCodeUpdateRange, L("Permissions:PromoCodeUpdateRange"));
        promoCodeGroup.AddPermission(GoTrackPermissions.PromoCodeDeactive, L("Permissions:PromoCodeDeactive"));
        promoCodeGroup.AddPermission(GoTrackPermissions.PromoCodeDetails, L("Permissions:PromoCodeDetails"));
        promoCodeGroup.AddPermission(GoTrackPermissions.PromoCodeIndex, L("Permissions:PromoCodeIndex"));

        var trackAccountSubscriptionGroup = context.AddGroup(
            GoTrackPermissions.TrackAccountSubscriptionGroupName,
            L("PermissionsGroup:TrackAccountSubscriptionGroup"));
        trackAccountSubscriptionGroup.AddPermission(
            GoTrackPermissions.TrackAccountSubscriptionIndex,
            L("Permissions:" + nameof(GoTrackPermissions.TrackAccountSubscriptionIndex)));
        trackAccountSubscriptionGroup.AddPermission(
            GoTrackPermissions.TrackAccountSubscriptionDetails,
            L("Permissions:" + nameof(GoTrackPermissions.TrackAccountSubscriptionDetails)));

        var settingsGroup = context.AddGroup(
            GoTrackPermissions.SettingsGroupName,
            L("PermissionsGroup:SettingsGroup"));
        settingsGroup.AddPermission(
            GoTrackPermissions.TrackAccountSubscriptionExpireReminderGet,
            L("Permissions:" + nameof(GoTrackPermissions.TrackAccountSubscriptionExpireReminderGet)));
        settingsGroup.AddPermission(
            GoTrackPermissions.TrackAccountSubscriptionExpireReminderUpdate,
            L("Permissions:" + nameof(GoTrackPermissions.TrackAccountSubscriptionExpireReminderUpdate)));
        settingsGroup.AddPermission(
            GoTrackPermissions.SmsBundleExpireReminderGet,
            L("Permissions:" + nameof(GoTrackPermissions.SmsBundleExpireReminderGet)));
        settingsGroup.AddPermission(
            GoTrackPermissions.SmsBundleExpireReminderUpdate,
            L("Permissions:" + nameof(GoTrackPermissions.SmsBundleExpireReminderUpdate)));
        
        var privacyPolicyGroup = context.AddGroup(
            GoTrackPermissions.PrivacyPolicyGroupName,
            L("PermissionsGroup:PrivacyPolicy"));

        privacyPolicyGroup.AddPermission(
            GoTrackPermissions.PrivacyPolicyGet,
            L("Permissions:PrivacyPolicyGet"));

        privacyPolicyGroup.AddPermission(
            GoTrackPermissions.PrivacyPolicyUpdate,
            L("Permissions:PrivacyPolicyUpdate"));


    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<GoTrackResource>(name);
    }
}