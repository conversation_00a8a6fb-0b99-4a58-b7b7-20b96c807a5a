using System;
using System.Collections.Generic;

namespace GoTrack.Payments.Discounts.DTOs;

public class CreateDiscountDto
{
    public string Name { get; set; } = string.Empty;
    
    public TargetType TargetType { get; set; }
    public decimal Value { get; set; }
    public bool IsPercentage { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public List<string>? PricingItemKeys { get; set; }

    public List<DiscountCriteriaDto> DiscountCriteriaDtos { get; set; } = [];
}
