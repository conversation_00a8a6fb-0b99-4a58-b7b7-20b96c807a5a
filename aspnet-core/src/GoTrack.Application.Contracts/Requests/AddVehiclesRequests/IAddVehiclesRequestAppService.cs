using GoTrack.Requests.AddVehiclesRequests.DTOs;
using System;
using System.Threading.Tasks;
using GoTrack.Requests.RenewSubscriptionRequests.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Requests.AddVehiclesRequests;

public interface IAddVehiclesRequestAppService : IApplicationService
{
    Task<PagedResultDto<AddVehiclesRequestDto>> GetListAsync(PagedResultRequestDto input);
    Task<AddVehiclesRequestDto> GetAsync(Guid id);
    Task<PagedResultDto<SubscriptionVehicleInfoSearchDto>> GetListVehiclesWithSearchAsync(Guid id,
        GetListNewVehiclesWithSearchDto input);
    Task InstallDevicesAsync(Guid id, AddVehiclesRequestsInstallDevicesDto input);
    Task FinishProcessingAsync(Guid id, AddVehiclesRequestsFinishProcessingDto input);
}
