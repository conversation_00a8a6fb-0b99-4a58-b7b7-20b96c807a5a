using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Requests.SmsBundleRenewalRequests.DTOs;

public class SmsBundleRenewalRequestDetailsDto : RequestDto
{
    public string OwnerFirstName { get; set; } = string.Empty;
    public string OwnerLastName { get; set; } = string.Empty;
    public string OwnerEmail { get; set; } = string.Empty;
    public Guid TrackAccountSubscriptionId { get; set; }
    public string TrackAccountSubscriptionTrackAccountName { get; set; } = string.Empty;
    public DateTime TrackAccountSubscriptionFrom { get; set; }
    public DateTime TrackAccountSubscriptionTo { get; set; }
    public Guid SmsBundleId { get; set; }
    public string SmsBundleName{ get; set; } = string.Empty;
    public int SmsBundleMessagesCount { get; set; }
    public decimal SmsBundlePrice{ get; set; }
    public SmsBundleRenewalStage SmsBundleRenewalStage { get; set; }
}