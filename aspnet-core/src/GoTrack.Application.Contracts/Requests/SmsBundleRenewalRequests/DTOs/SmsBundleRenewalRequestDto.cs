using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Requests.SmsBundleRenewalRequests.DTOs;

public class SmsBundleRenewalRequestDto : RequestDto
{
    public Guid TrackAccountSubscriptionId { get; set; }
    public Guid SmsBundleId { get; set; }
    public SmsBundleRenewalStage SmsBundleRenewalStage { get; set; }
    public string SmsBundleName{ get; set; } = string.Empty;
    public int SmsBundleMessagesCount { get; set; }
    public decimal SmsBundlePrice{ get; set; }
}