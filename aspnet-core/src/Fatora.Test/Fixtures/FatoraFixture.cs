using Fatora.Abstractions.DTO.CreatePaymentDtos;
using Fatora.Abstractions.DTO.RevesalPaymentDto;
using Fatora.Abstractions.Enums;
using Fatora.Options;
using Microsoft.Extensions.Logging;

namespace Fatora.Test.Fixtures;

public class FatoraFixture
{
    public FatoraService FatoraService { get; private set; }

    private FatoraOptions FatoraCorrectTestOption => 
        new("https://egate-t.fatora.me/api/", "gotrack", "gotrack@123", "14740084");

    public CreatePaymentRequestDto CreatePaymentRequestDto =>
        new(FatoraLanguage.Arabic, 1250, null, "https://webhook.site/58974589-95d1-42b5-8318-473dac0132c9", false, new Guid(), null);

    public ReversalPaymentRequestDto ReversalPaymentRequestDto =>
        new(FatoraLanguage.Arabic, Guid.Parse("d93399d5-67e8-4912-99a2-84b871a4fa29"));

    public FatoraFixture()
    {
        var logger = Mock.Of<ILogger<FatoraService>>();

        var options = Microsoft.Extensions.Options.Options.Create<FatoraOptions>(FatoraCorrectTestOption);

        FatoraService = new FatoraService(logger, options);
    }
}