using GoTrack.EntityFrameworkCore;
using GoTrack.TrackAccounts.BusinessTrackAccounts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.TrackAccounts.EntityConfigurations.BusinessTrackAccounts;

public class BusinessTrackAccountEntityConfiguration : IEntityTypeConfiguration<BusinessTrackAccount>
{
    public void Configure(EntityTypeBuilder<BusinessTrackAccount> builder)
    {
        builder.ToGoTrackTable();
        builder.ConfigureByConvention();

        builder.OwnsOne(a => a.CompanyAddress);
    }
}
