using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.TrackAccounts.EntityConfigurations;

public class TrackAccountEntityConfiguration : IEntityTypeConfiguration<TrackAccount>
{
    public void Configure(EntityTypeBuilder<TrackAccount> builder)
    {
        builder.ToGoTrackTable();

        var userTrackAccountAssociationsNavigation = builder.Metadata.FindNavigation(nameof(TrackAccount.UserTrackAccountAssociations));

        userTrackAccountAssociationsNavigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

        builder.HasMany(ta => ta.UserTrackAccountAssociations);

        var trackAccountSubscriptionsNavigation = builder.Metadata.FindNavigation(nameof(TrackAccount.TrackAccountSubscriptions));

        trackAccountSubscriptionsNavigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

        builder.ConfigureByConvention();
    }
}
