using GoTrack.EntityFrameworkCore;
using GoTrack.TrackAccounts.PersonalTrackAccounts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.TrackAccounts.EntityConfigurations.PersonalTrackAccounts;

public class PersonalTrackAccountEntityConfiguration : IEntityTypeConfiguration<PersonalTrackAccount>
{
    public void Configure(EntityTypeBuilder<PersonalTrackAccount> builder)
    {
        builder.ToGoTrackTable();
        builder.ConfigureByConvention();
    }
}
