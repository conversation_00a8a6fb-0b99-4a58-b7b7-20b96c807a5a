using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class RenewSubscriptionRequest : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CreatedTrackAccountSubscriptionId",
                table: "GoTrackRenewSubscriptionRequests",
                type: "char(36)",
                nullable: true,
                collation: "ascii_general_ci");

            migrationBuilder.CreateIndex(
                name: "IX_GoTrackVehicles_TrackAccountId",
                table: "GoTrackVehicles",
                column: "TrackAccountId");

            migrationBuilder.AddForeignKey(
                name: "FK_GoTrackVehicles_GoTrackTrackAccounts_TrackAccountId",
                table: "GoTrackVehicles",
                column: "TrackAccountId",
                principalTable: "GoTrackTrackAccounts",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_GoTrackVehicles_GoTrackTrackAccounts_TrackAccountId",
                table: "GoTrackVehicles");

            migrationBuilder.DropIndex(
                name: "IX_GoTrackVehicles_TrackAccountId",
                table: "GoTrackVehicles");

            migrationBuilder.DropColumn(
                name: "CreatedTrackAccountSubscriptionId",
                table: "GoTrackRenewSubscriptionRequests");
        }
    }
}
