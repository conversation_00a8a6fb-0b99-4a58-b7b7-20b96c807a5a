using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class ChangeNameOfDisassembleTrackingDeviceAlertDefinition : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GoTrackDisassembleTrackingDeviceDefinitions");

            migrationBuilder.CreateTable(
                name: "GoTrackDisassembleTrackingDeviceAlertDefinitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoTrackDisassembleTrackingDeviceAlertDefinitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GoTrackDisassembleTrackingDeviceAlertDefinitions_GoTrackAler~",
                        column: x => x.Id,
                        principalTable: "GoTrackAlertDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GoTrackDisassembleTrackingDeviceAlertDefinitions");

            migrationBuilder.CreateTable(
                name: "GoTrackDisassembleTrackingDeviceDefinitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoTrackDisassembleTrackingDeviceDefinitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GoTrackDisassembleTrackingDeviceDefinitions_GoTrackAlertDefi~",
                        column: x => x.Id,
                        principalTable: "GoTrackAlertDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");
        }
    }
}
