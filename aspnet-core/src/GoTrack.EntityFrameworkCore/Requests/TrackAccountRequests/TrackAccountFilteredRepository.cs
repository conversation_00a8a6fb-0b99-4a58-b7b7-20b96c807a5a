using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.EntityFrameworkCore;
using GoTrack.TrackAccounts;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace GoTrack.Requests.TrackAccountRequests;

public class TrackAccountFilteredRepository<TEntity> : EfCoreRepository<GoTrackDbContext, TEntity, Guid>
    where TEntity : class, IEntity<Guid>, IHaveTrackAccount
{
    private bool IsTrackAccountFilterEnabled => DataFilter?.IsEnabled<IHaveTrackAccount>() ?? false;

    public TrackAccountFilteredRepository(IDbContextProvider<GoTrackDbContext> dbContextProvider) : base(
        dbContextProvider)
    {
    }

    public override async Task<IQueryable<TEntity>> GetQueryableAsync()
    {
        var queryable = await base.GetQueryableAsync();
        return queryable.Where(x =>
            !IsTrackAccountFilterEnabled ||
            x.TrackAccountId == LazyServiceProvider.LazyGetRequiredService<ICurrentTrackAccount>().Id);
    }
}