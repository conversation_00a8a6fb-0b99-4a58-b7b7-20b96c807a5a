using GoTrack.EntityFrameworkCore;
using GoTrack.Routes.RouteViewModels;
using GoTrack.StopPoints;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities;

namespace GoTrack.Routes;

public class RouteReadRepository : IRouteReadRepository, IScopedDependency
{
    private readonly GoTrackDbContext _context;
    private DbSet<Route> _dbSet => _context.Set<Route>();
    
    public RouteReadRepository(GoTrackDbContext context)
    {
        _context = context;
    }

    public async Task<RouteViewModel> GetRouteViewModelAsync(Guid routeId)
    {
        var route = await _dbSet.Include(x => x.StopPoints).FirstOrDefaultAsync(x => x.Id == routeId)
            ?? throw new EntityNotFoundException(typeof(Route));

        var stopPointIdsInRoute = route.StopPoints.Select(x => x.StopPointId).ToList();

        var routeStopPoints = await _context.Set<StopPoint>()
            .Where(x => stopPointIdsInRoute.Contains(x.Id))
            .ToListAsync();

        return RouteViewModel.GetRouteViewModelFromRoute(route, routeStopPoints);
    }
}
