using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.VehicleGroups.EntityConfiguration;

public class VehicleGroupEntityConfiguration : IEntityTypeConfiguration<VehicleGroup>
{
    public void Configure(EntityTypeBuilder<VehicleGroup> builder)
    {
        builder.ConfigureByConvention();

        var navigation = builder.Metadata.FindNavigation(nameof(VehicleGroup.VehicleGroupVehicles));
        navigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

        builder.HasMany(x => x.VehicleGroupVehicles);
    
        builder.ToGoTrackTable();
    }
}
