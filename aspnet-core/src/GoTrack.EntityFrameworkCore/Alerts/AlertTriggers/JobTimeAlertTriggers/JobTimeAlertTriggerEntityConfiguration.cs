using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers;

public class JobTimeAlertTriggerEntityConfiguration : IEntityTypeConfiguration<JobTimeAlertTrigger>
{
    public void Configure(EntityTypeBuilder<JobTimeAlertTrigger> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
