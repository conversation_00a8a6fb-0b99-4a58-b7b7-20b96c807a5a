using System;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.TrackAccounts;
using Notify.Provider.Sms;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Users;

namespace GoTrack.Sms;

public class UserPhoneNumberProvider : IUserPhoneNumberProvider, ITransientDependency
{
    private readonly IExternalUserLookupServiceProvider _userLookupServiceProvider;
    private readonly IDataFilter _dataFilter;

    public UserPhoneNumberProvider(IExternalUserLookupServiceProvider userLookupServiceProvider, IDataFilter dataFilter)
    {
        _userLookupServiceProvider = userLookupServiceProvider;
        _dataFilter = dataFilter;
    }

    public virtual async Task<string?> GetAsync(Guid userId)
    {
        using var _ = _dataFilter.Disable<IHaveTrackAccount>();
        using var __ = _dataFilter.Disable<IHostTenantUserFilter>();
        using var ___ = _dataFilter.Disable<ICustomerUserFilter>();
        
        var userData = await _userLookupServiceProvider.FindByIdAsync(userId);

        if (userData is null || !userData.PhoneNumberConfirmed || userData.PhoneNumber.IsNullOrWhiteSpace())
            return null;

        return userData.PhoneNumber;
    }
}