using GoTrack.EntityFrameworkCore;
using GoTrack.Msisdns;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.UserTrackAccountAssociations.EntityConfiguration;

public class UserTrackAccountAssociationEntityConfiguration : IEntityTypeConfiguration<UserTrackAccountAssociation>
{
    private readonly IMsisdnManager _msisdnManager;

    public UserTrackAccountAssociationEntityConfiguration(IMsisdnManager msisdnManager)
    {
        _msisdnManager = msisdnManager;
    }

    public void Configure(EntityTypeBuilder<UserTrackAccountAssociation> builder)
    {
        builder.ConfigureByConvention();

        builder.Property(observer => observer.PhoneNumber)
            .HasConversion(
                // Serialize Msisdn to string using its ToString() method
                msisdn => msisdn == null ? string.Empty : msisdn.ToString(),

                // Deserialize from string format back to Msisdn
                str => str == string.Empty ? null : _msisdnManager.Create(str)
            )
            .IsRequired(false);

        builder.HasMany(x => x.Observations);

        builder.ToGoTrackTable();
    }
}
