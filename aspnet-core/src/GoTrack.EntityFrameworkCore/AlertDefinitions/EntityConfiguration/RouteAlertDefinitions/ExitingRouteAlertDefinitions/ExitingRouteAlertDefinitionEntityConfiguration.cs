using GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions;
using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration.RouteAlertDefinitions.ExitingRouteAlertDefinitions;

public class ExitingRouteAlertDefinitionEntityConfiguration : IEntityTypeConfiguration<ExitingRouteAlertDefinition>
{
    public void Configure(EntityTypeBuilder<ExitingRouteAlertDefinition> builder)
    {
        builder.ConfigureByConvention();

        var navigation = builder.Metadata.FindNavigation(nameof(ExitingRouteAlertDefinition.RouteAlertRoutes));
        navigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

        builder.HasMany(x => x.RouteAlertRoutes);

        builder.ToGoTrackTable();
    }
}
