using System.Drawing;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.RenewTrackAccountSubscriptions.EntityConfiguration;
public class RenewTrackAccountSubscriptionEntityConfiguration : IEntityTypeConfiguration<RenewSubscriptionRequest>
{
    public void Configure(EntityTypeBuilder<RenewSubscriptionRequest> builder)
    {
        builder.ConfigureByConvention();
        builder.ToGoTrackTable();
        builder.OwnsMany(r => r.NewTrackVehicles, t =>
        {
            t.ToGoTrackTable("NewSubscriptionRequestTrackVehicles");

            t.OwnsOne(tv => tv.LicensePlate);
            t.Property(v => v.Color).HasConversion(
                c => ColorTranslator.ToWin32(c),
                i => ColorTranslator.FromWin32(i)
            );
        });
    }
}