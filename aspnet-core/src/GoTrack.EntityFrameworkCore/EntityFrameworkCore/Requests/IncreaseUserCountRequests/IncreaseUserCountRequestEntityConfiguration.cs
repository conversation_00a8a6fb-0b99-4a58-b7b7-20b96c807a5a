using GoTrack.Requests.IncreaseUserCountRequests;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.Requests.IncreaseUserCountRequests;

public class IncreaseUserCountRequestEntityConfiguration: IEntityTypeConfiguration<IncreaseUserCountRequest>
{
    public void Configure(EntityTypeBuilder<IncreaseUserCountRequest> builder)
    {
        builder.ToGoTrackTable();
        builder.ConfigureByConvention();
    }
}