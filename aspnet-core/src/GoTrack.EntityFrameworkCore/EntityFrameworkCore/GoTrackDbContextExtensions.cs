using System.Drawing;
using GoTrack.AlertDefinitions.EntityConfiguration;
using GoTrack.AlertDefinitions.EntityConfiguration.DisassembleTrackingDevices;
using GoTrack.AlertDefinitions.EntityConfiguration.ExceedingSpeedAlertDefinitions;
using GoTrack.AlertDefinitions.EntityConfiguration.JobTimeAlertDefinitions;
using GoTrack.AlertDefinitions.EntityConfiguration.RouteAlertDefinitions.ExitingRouteAlertDefinitions;
using GoTrack.AlertDefinitions.EntityConfiguration.RouteAlertDefinitions.RouteAlertRoutes;
using GoTrack.AlertDefinitions.EntityConfiguration.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.AlertDefinitions.EntityConfiguration.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using GoTrack.AlertDefinitions.EntityConfiguration.ZoneAlertDefinitions.ZoneAlertGeoZones;
using GoTrack.Alerts.AlertLogs;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers;
using GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers;
using GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers;
using GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers;
using GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers;
using GoTrack.Devices.EntityConfiguration;
using GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Payments;
using GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Requests.AddVehiclesRequests;
using GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Requests.TrackAccountRequests;
using GoTrack.EntityFrameworkCore.RenewTrackAccountSubscriptions.EntityConfiguration;
using GoTrack.EntityFrameworkCore.Requests.IncreaseUserCountRequests;
using GoTrack.FCMDevices.EntityConfiguration;
using GoTrack.GeoNodes;
using GoTrack.GeoZones;
using GoTrack.Identity;
using GoTrack.Msisdns;
using GoTrack.Observations.EntityConfiguration;
using GoTrack.PermissionGrants.EntityConfigurations;
using GoTrack.PrivacyPolicies.EntityConfiguration;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.SmsBundleRenewalRequests;
using GoTrack.Routes.EntityConfiguration;
using GoTrack.SmsBundles.EntityConfiguration;
using GoTrack.StopPoints.EntityConfiguration;
using GoTrack.TrackableEntities.EntityConfiguration;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.EntityConfigurations;
using GoTrack.TrackAccounts.EntityConfigurations.BusinessTrackAccounts;
using GoTrack.TrackAccounts.EntityConfigurations.PersonalTrackAccounts;
using GoTrack.TrackAccounts.EntityConfigurations.TrackAccountSubscriptions;
using GoTrack.Trips.EntityConfiguration;
using GoTrack.TripTemplates.EntityConfiguraiton;
using GoTrack.UserDeviceTokens.EntityConfiguraiton;
using GoTrack.UserTrackAccountAssociations.EntityConfiguration;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.VehicleGroups.EntityConfiguration;
using GoTrack.Vehicles;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore;

public static class GoTrackDbContextExtensions
{
    public static void ConfigureGoTrackEntities(this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        /* Configure your own tables/entities inside here */

        builder.ApplyConfiguration(new ZoneAlertGeoZoneEntityConfiguration());
        builder.ApplyConfiguration(new AlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new ExceedingSpeedAlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new JobTimeAlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new EnteringZoneAlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new ExitingZoneAlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new AlertLogEntityConfiguration());
        builder.ApplyConfiguration(new TrackableEntityAssociationEntityConfiguration());
        builder.ApplyConfiguration(new VehicleGroupEntityConfiguration());
        builder.ApplyConfiguration(new VehicleGroupVehicleEntityConfiguration());
        builder.ApplyConfiguration(new ObservationEntityConfiguration());
        builder.ApplyConfiguration(new UserTrackAccountAssociationEntityConfiguration(new MsisdnManager()));
        builder.ApplyConfiguration(new AlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new EnteringZoneAlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new ExitingZoneAlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new ExceedingSpeedAlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new JobTimeAlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new DisassembleTrackingDeviceAlertDefinitionEntityConfiguration());
        builder.ApplyConfiguration(new RouteEntityConfiguration());
        builder.ApplyConfiguration(new StopPointEntityConfiguration());
        builder.ApplyConfiguration(new TripEntityConfiguration());
        builder.ApplyConfiguration(new TripTemplateEntityConfiguration());
        builder.ApplyConfiguration(new UserFatoraPaymentsEntityConfiguration());
        builder.ApplyConfiguration(new TrackAccountEntityConfiguration());
        builder.ApplyConfiguration(new BusinessTrackAccountEntityConfiguration());
        builder.ApplyConfiguration(new PersonalTrackAccountEntityConfiguration());
        builder.ApplyConfiguration(new TrackAccountSubscriptionEntityConfiguration());
        builder.ApplyConfiguration(new PermissionGrantEntityConfiguration());
        builder.ApplyConfiguration(new FcmDeviceEntityConfiguration());
        builder.ApplyConfiguration(new SmsBundleEntityConfiguration());
        builder.ApplyConfiguration(new DeviceEntityConfiguration());
        builder.ApplyConfiguration(new DeviceStatusLogEntityConfiguration());
        builder.ApplyConfiguration(new BillEntityConfiguration());
        builder.ApplyConfiguration(new PricingItemEntityConfiguration());
        builder.ApplyConfiguration(new DiscountEntityConfiguration());
        builder.ApplyConfiguration(new IncreaseUserCountRequestEntityConfiguration());
        builder.ApplyConfiguration(new RenewTrackAccountSubscriptionEntityConfiguration());
        builder.ApplyConfiguration(new SmsBundleRenewalRequestEntityConfiguration());
        builder.ApplyConfiguration(new TrackAccountRequestEntityConfiguration());
        builder.ApplyConfiguration(new AddVehiclesRequestEntityConfiguration());
        builder.ApplyConfiguration(new UserDeviceTokensEntityConfiguration());
        builder.ApplyConfiguration(new ExitingRouteAlertDefinitionEntityConfiguration()); 
        builder.ApplyConfiguration(new ExitingRouteAlertTriggerEntityConfiguration());
        builder.ApplyConfiguration(new RouteAlertRouteEntityConfiguration());
        builder.ApplyConfiguration(new PromoCodeConfiguration());
        builder.ApplyConfiguration(new PrivacyPolicyEntityConfiguration());
        
        builder.Entity<TrackAccount>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
        });

        builder.Entity<Vehicle>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();

            var navigation = b.Metadata.FindNavigation(nameof(Vehicle.VehicleGroupVehicles));
            navigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

            b.Property(v => v.Color).HasConversion(
                c => ColorTranslator.ToWin32(c)
                , i => ColorTranslator.FromWin32(i));

            b.OwnsOne(v => v.LicensePlate);
            //b.OwnsMany(v => v.VehicleGroupVehicles);
            // b.OwnsOne(v => v.Specification);
        });

        builder.Entity<GeoNode>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
        });

        builder.Entity<IdentityUserProfile>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
            b.Property(e => e.PreferredLanguage)
                .HasDefaultValue(SupportedLanguageExtensions.DefaultLanguage); 
            b.OwnsOne(x => x.Address);
        });


        builder.Entity<BusinessAccountSubscriptionRequest>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();

            b.OwnsOne(r => r.CompanyAddress);
            b.OwnsMany(r => r.TrackVehicles, t =>
            {
                t.ToGoTrackTable("BusinessAccountSubscriptionRequestTrackVehicles");

                t.OwnsOne(tv => tv.LicensePlate);
                t.Property(v => v.Color).HasConversion(
                    c => ColorTranslator.ToWin32(c),
                    i => ColorTranslator.FromWin32(i)
                );
            });
        });

        builder.Entity<PersonalAccountSubscriptionRequest>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();

            b.OwnsOne(x => x.Address);
            
            b.OwnsMany(r => r.TrackVehicles, t =>
            {
                t.ToGoTrackTable("PersonalAccountSubscriptionRequestTrackVehicles");

                t.OwnsOne(tv => tv.LicensePlate);
                t.Property(v => v.Color).HasConversion(
                    c => ColorTranslator.ToWin32(c),
                    i => ColorTranslator.FromWin32(i)
                );
            });
        });

        builder.Entity<Request>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
        });

        builder.Entity<RequestNote>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
        });

        builder.Entity<GeoZone>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
            b.OwnsOne(v => v.Polyline);
        });

        builder.Entity<VehicleDeviceEventLog>(b =>
        {
            b.ToGoTrackTable();
            b.ConfigureByConvention();
        });

    }

    private static void ToGoTrackTable<TEntity>(this EntityTypeBuilder<TEntity> entityTypeBuilder, string name) where TEntity : class
    {
        entityTypeBuilder.ToTable(GoTrackConsts.DbTablePrefix + name, GoTrackConsts.DbSchema);
    }

    public static void ToGoTrackTable<TEntity>(this EntityTypeBuilder<TEntity> entityTypeBuilder) where TEntity : class
    {
        entityTypeBuilder.ToTable(GoTrackConsts.DbTablePrefix + entityTypeBuilder.Metadata.ClrType.Name + "s",
            GoTrackConsts.DbSchema);
    }

    public static void ToGoTrackTable<TOwnerEntity, TDependentEntity>(
        this OwnedNavigationBuilder<TOwnerEntity, TDependentEntity> ownedNavigationBuilder, string name)
        where TOwnerEntity : class
        where TDependentEntity : class
    {
        ownedNavigationBuilder.ToTable(GoTrackConsts.DbTablePrefix + name, GoTrackConsts.DbSchema);
    }
}