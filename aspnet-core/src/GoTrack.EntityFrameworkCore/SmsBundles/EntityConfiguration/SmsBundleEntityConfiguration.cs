using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.SmsBundles.EntityConfiguration;

public class SmsBundleEntityConfiguration : IEntityTypeConfiguration<SmsBundle>
{
    public void Configure(EntityTypeBuilder<SmsBundle> builder)
    {
        builder.ConfigureByConvention();
        builder.ToGoTrackTable();    }
}