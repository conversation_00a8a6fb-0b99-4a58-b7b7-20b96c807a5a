<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\common.props" />

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>GoTrack</RootNamespace>
        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.Identity.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="8.3.0" />
        <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.4" />
        <ProjectReference Include="..\..\modules\Notify\src\Notify.Domain.Shared\Notify.Domain.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Localization\GoTrack\*.json" />
        <EmbeddedResource Include="Localization\UserPortal\*.json" />
        <EmbeddedResource Include="Localization\GeoNode\*.json" />
		<EmbeddedResource Include="Localization\Permissions\*.json" />
		<EmbeddedResource Include="Localization\GoTrackErrors\*.json" />
        <Content Remove="Localization\GoTrack\*.json" />
	</ItemGroup>

    <ItemGroup>
        <Content Include="GeoNodes\cities_tree.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

</Project>
