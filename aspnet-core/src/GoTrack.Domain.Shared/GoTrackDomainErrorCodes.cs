namespace GoTrack;

public static class GoTrackDomainErrorCodes
{
    /* You can add your business exception error codes here, as constants */
    public const string MsisdnInvalidFormat = "GoTrack:Error.MsisdnInvalidFormat";
    public const string OtpInvalid = "GoTrack:Error.OtpInvalid";
    public const string InvalidImageFormat = "GoTrack:Error.InvalidImageFormat";
    public const string InvalidImageSize = "GoTrack:Error.InvalidImageSize";
    public const string DuplicateLicensePlate = "GoTrack:Error.DuplicateLicensePlate";
    public const string ParentGeoNodeNotFound = "GoTrack:Error.ParentGeoNodeNotFound";
    public const string ChildGeoNodeTypeShouldNotBeCountry = "GoTrack:Error.ChildGeoNodeTypeShouldNotBeCountry";
    public const string ParentGeoNodeShouldBeCountry = "GoTrack:Error.ParentGeoNodeShouldBeCountry";
    public const string ParentGeoNodeShouldBeGovernorate = "GoTrack:Error.ParentGeoNodeShouldBeGovernorate";
    public const string ParentGeoNodeShouldBeCity = "GoTrack:Error.ParentGeoNodeShouldBeCity";
    public const string AddressEnteredIsInvalid = "GoTrack:Error.AddressEnteredIsInvalid";
    public const string SubscriptionInvalidDateRange = "GoTrack:Error.SubscriptionInvalidDateRange";
    public const string SubscriptionInvalidVehicles = "GoTrack:Error.SubscriptionInvalidVehicles";
    public const string InvalidRequestStatusOnlyPendingCanCanceled = "GoTrack:Error.InvalidRequestStatusOnlyPendingCanCanceled";
    public const string MustHaveTwoCoordinatesAtLeast = "GoTrack:Error.MustHaveTwoCoordinatesAtLeast";
    public const string InValidPolylineAlgorithm = "GoTrack:Error.InValidPolylineAlgorithm";
    public const string MustHaveThreeCoordinatesAtLeast = "GoTrack:Error.MustHaveThreeCoordinatesAtLeast";
    public const string VehicleNotInGroup = "GoTrack:Error.VehicleNotInGroup";
    public const string VehicleAlreadyExistsInThisGroup = "GoTrack:Error.VehicleAlreadyExistsInThisGroup";
    public const string TrackAccountIdNotFound = "GoTrack:Error.TrackAccountIdNotFound";
    public const string DuplicateGeoZoneName = "GoTrack:Error.DuplicateGeoZoneName";
    public const string DuplicateGeoZonePolyLine = "GoTrack:Error.DuplicateGeoZonePolyLine";
    public const string DeviceIsAlreadyInstalled = "GoTrack:Error.DeviceIsAlreadyInstalled";
    public const string DeviceIsAlreadyUninstalled = "GoTrack:Error.DeviceIsAlreadyUninstalled";
    public const string InitialStatusOfAssigningDeviceToVehicleMustBeInstalled = "GoTrack:Error.InitialStatusOfAssigningDeviceToVehicleMustBeInstalled";
    public const string NoInstalledDeviceFoundForVehicle = "GoTrack:Error.NoInstalledDeviceFoundForVehicle";
    public const string OneOrMoreVehiclesNotFound = "GoTrack:Error.OneOrMoreVehiclesNotFound";
    public const string OneOrMoreVehiclesDoNotHaveADevice = "GoTrack:Error.OneOrMoreVehiclesDoNotHaveADevice";
    public const string ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver = "GoTrack:Error.ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver";
    public const string NoAccess = "GoTrack:Error.NoAccess";
    public const string OneOrMoreObserversNotFound = "GoTrack:Error.OneOrMoreObserversNotFound";
    public const string ObserverWithVehicleOrVehicleGroupAlreadyExists = "GoTrack:Error.ObserverWithVehicleOrVehicleGroupAlreadyExists";
    public const string WrongWayToAddOwnerToTrackAccount = "GoTrack:Error.WrongWayToAddOwnerToTrackAccount";
    public const string AlertTypeIsInvalid = "GoTrack:Error.AlertTypeIsInvalid";
    public const string AlertOnVehicleOrVehicleGroupAlreadyExists = "GoTrack:Error.AlertOnVehicleOrVehicleGroupAlreadyExists";
    public const string RouteDoesNotContainStopPoint = "GoTrack:Error.RouteDoesNotContainStopPoint";
    public const string TripTemplateWithVehicleOrVehicleGroupAlreadyExists = "GoTrack:Error.TripTemplateWithVehicleOrVehicleGroupAlreadyExists";
    public const string PriceCannotBeNegative = "GoTrack:Error.PriceCannotBeNegative";
    public const string InvalidSubscriptionStage = "GoTrack:Error.InvalidSubscriptionStage";
    public const string ValueIsAlreadySet = "GoTrack:Error.ValueIsAlreadySet";
    public const string InvalidDateRange = "GoTrack:Error.InvalidDateRange";
    public const string ObserverCountTooLow = "GoTrack:Error.ObserverCountTooLow";
    public const string ObserverLimitReached = "GoTrack:Error.ObserverLimitReached";
    public const string FcmTokenMustBeUnique = "GoTrack:Error.FcmTokenMustBeUnique";
    public const string InvalidMessagesCount = "GoTrack:Error.InvalidMessagesCount";
    public const string InvalidPrice = "GoTrack:Error.InvalidPrice";
    public const string InvalidSubscriptionDuration = "GoTrack:Error.InvalidSubscriptionDuration";
    //public const string InvalidDiscountRate = "GoTrack:Error.InvalidDiscountRate";
    //public const string CannotApplyDiscountAtThisStage = "GoTrack:Error.CannotApplyDiscountAtThisStage";
    public const string InvalidSmsBundlesCount = "GoTrack:Error.InvalidSmsBundlesCount";
    public const string InvalidUserCount = "GoTrack:Error.InvalidUserCount";
    public const string NotificationTimeMustBeAfterLastOne = "GoTrack:Error.NotificationTimeMustBeAfterLastOne";
    public const string TheDeviceStatusMustBeInoperative = "GoTrack:Error.TheDeviceStatusMustBeInoperative";
    public const string TheDeviceStatusWithImeiMustBeInoperative = "GoTrack:Error.TheDeviceStatusWithImeiMustBeInoperative";
    public const string SomeLicensePlateDoNotMatchWithRequest = "GoTrack:Error.SomeLicensePlateDoNotMatchWithRequest";
    public const string InitialStateCannotBeExpired = "GoTrack:Error.InitialStateCannotBeExpired";
    public const string DuplicateEntriesFoundForLicensePlateSerialAndSubClass = "GoTrack:Error.DuplicateEntriesFoundForLicensePlateSerialAndSubClass";
    public const string TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest = "GoTrack:Error.TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest";
    public const string CreatedTrackAccountAlreadySet = "GoTrack:Error.CreatedTrackAccountAlreadySet";
    public const string OwnerIDHasAlreadyBeenSet = "GoTrack:Error.OwnerIDHasAlreadyBeenSet";
    public const string DeviceStatusIsAlreadyActive = "GoTrack:Error.DeviceStatusIsAlreadyActive";
    public const string DeviceStatusIsAlreadyDeactivated = "GoTrack:Error.DeviceStatusIsAlreadyDeactivated";
    public const string DeviceAlreadyOwned = "GoTrack:Error.DeviceAlreadyOwned";
    public const string DeviceWithImeiAlreadyOwned = "GoTrack:Error.DeviceWithImeiAlreadyOwned";
    public const string DeviceStatusInvalidForNonTracking = "GoTrack:Error.DeviceStatusInvalidForNonTracking";
    public const string DeviceStatusWithImeiInvalidForNonTracking = "GoTrack:Error.DeviceStatusWithImeiInvalidForNonTracking";
    public const string TrackingDeviceOwnerMismatch = "GoTrack:Error.TrackingDeviceOwnerMismatch";
    public const string TrackingDeviceWithImeiOwnerMismatch = "GoTrack:Error.TrackingDeviceWithImeiOwnerMismatch";
    public const string DeviceCannotBeDeactivated = "GoTrack:Error.DeviceCannotBeDeactivated";
    public const string DeviceCannotBeActivated = "GoTrack:Error.DeviceCannotBeActivated";
    public const string CannotActivateSubscription = "GoTrack:Error.CannotActivateSubscription";
    public const string InvalidUserCountRequestStage = "GoTrack:Error.InvalidUserCountRequestStage";
    public const string InactiveSubscription = "GoTrack:Error.InactiveSubscription";
    public const string DuplicateUserCountRequest = "GoTrack:Error.DuplicateUserCountRequest";
    public const string CannotApproveBill = "GoTrack:Error.CannotApproveBill";
    public const string CannotRejectBill = "GoTrack:Error.CannotRejectBill";
    public const string CannotMarkBillAsPaid = "GoTrack:Error.CannotMarkBillAsPaid";
    public const string CannotAddBillLineItem = "GoTrack:Error.CannotAddBillLineItem";
    public const string CannotApplyDiscountToBill = "GoTrack:Error.CannotApplyDiscountToBill";
    public const string DiscountInvalidDateRange = "GoTrack:Error.DiscountInvalidDateRange";
    public const string DiscountRequestIdRequired = "GoTrack:Error.DiscountRequestIdRequired";
    public const string DiscountPricingItemKeyRequired = "GoTrack:Error.DiscountPricingItemKeyRequired";
    public const string DiscountDurationInMonthRequired = "GoTrack:Error.DiscountDurationInMonthRequired";
    public const string DiscountInvalidEndDate = "GoTrack:Error.DiscountInvalidEndDate";
    public const string DiscountPricingItemNotFound = "GoTrack:Error.DiscountPricingItemNotFound";
    //public const string DiscountRequestNotFound = "GoTrack:Error.DiscountRequestNotFound";
    public const string SubscriptionRequestUnderReview = "GoTrack:Error.SubscriptionRequestUnderReview";
    public const string TrackerInstallationLocationRequired = "GoTrack:Error.TrackerInstallationLocationRequired";
    public const string UserRemovalListRequired = "GoTrack:Error.UserRemovalListRequired";
    public const string DeviceRequestsRequired = "GoTrack:Error.DeviceRequestsRequired";
    public const string NewVehiclesRequired = "GoTrack:Error.NewVehiclesRequired";
    public const string SubscriptionStillPending = "GoTrack:Error.SubscriptionStillPending";
    public const string SubscriptionAlreadyActive = "GoTrack:Error.SubscriptionAlreadyActive";
    public const string DeviceRequiredForActivation = "GoTrack:Error.DeviceRequiredForActivation";
    public const string InvalidSubscriptionStateForActivation = "GoTrack:Error.InvalidSubscriptionStateForActivation";
    public const string InvalidRenewalStage = "GoTrack:Error.InvalidRenewalStage";
    public const string DuplicateSmsBundleRenewalRequest = "GoTrack:Error.DuplicateSmsBundleRenewalRequest";
    public const string PaymentNotAllowed = "GoTrack:Error.PaymentNotAllowed";
    public const string CannotSetBillAsPendingApproval = "GoTrack:Error.CannotSetBillAsPendingApproval";
    public const string RequestDoesNotSupportPayments = "GoTrack:Error.RequestDoesNotSupportPayments";
    public const string BillAlreadyAssigned = "GoTrack:Error.BillAlreadyAssigned";
    public const string BillNotAssigned = "GoTrack:Error.BillNotAssigned";
    public const string DuplicateVehicle = "GoTrack:Error.DuplicateVehicle";
    public const string DuplicateDevice = "GoTrack:Error.DuplicateDevice";
    public const string InvalidRequestStatus = "GoTrack:Error.InvalidRequestStatus";
    public const string PromoCodeStartDateMustBeInFuture = "GoTrack:Error.PromoCodeStartDateMustBeInFuture";
    public const string ReversalPaymentFailed = "GoTrack:Error.ReversalPaymentFailed";
    public const string DeviceTokenNotFound = "GoTrack:Error.DeviceTokenNotFound";
    public const string NoClientSecretCanBeSetForPublicApplications = "GoTrack:Error.NoClientSecretCanBeSetForPublicApplications";
    public const string TheClientSecretIsRequiredForConfidentialApplications = "GoTrack:Error.TheClientSecretIsRequiredForConfidentialApplications";
    public const string InvalidRedirectUri = "GoTrack:Error.InvalidRedirectUri";
    public const string InvalidPostLogoutRedirectUri = "GoTrack:Error.InvalidPostLogoutRedirectUri";
    public const string RequestedMonthsCannotBeNull = "GoTrack:Error.RequestedMonthsCannotBeNull";
    public const string PriceNotSetFor = "GoTrack:Error.PriceNotSetFor";
    public const string InvalidRequestType = "GoTrack:Error.InvalidRequestType";
    public const string InvalidKey = "Invalid Key";
    public const string YouAreNotSupposedToDoThat = "You are not supposed to do that. >:(";
    public const string RequestCanOnlyBeRejectedIfPending = "GoTrack:Error.RequestCanOnlyBeRejectedIfPending";
    public const string RequestCanOnlyBeStartedIfPending = "GoTrack:Error.RequestCanOnlyBeStartedIfPending";
    public const string RequestCanOnlyBeFinishedIfProcessing = "GoTrack:Error.RequestCanOnlyBeFinishedIfProcessing";
    public const string EmailVerificationTokenExpired = "GoTrack:Error.EmailVerificationTokenExpired";
    public const string EmailVerificationEmailMismatch = "GoTrack:Error.EmailVerificationEmailMismatch";
    public const string EmailVerificationAlreadyConfirmed = "GoTrack:Error.EmailVerificationAlreadyConfirmed";
    public const string MalformedEmailVerificationToken = "GoTrack:Error.MalformedEmailVerificationToken";
    public const string DiscountSpecificationDurationInvalid = "GoTrack:Error.DiscountSpecificationDurationInvalid";
    public const string DiscountSpecificationPricingItemInvalid = "GoTrack:Error.DiscountSpecificationPricingItemInvalid";
    public const string DiscountSpecificationRequestTypeInvalid = "GoTrack:Error.DiscountSpecificationRequestTypeInvalid";
    public const string DiscountSpecificationUserCountInvalid = "GoTrack:Error.DiscountSpecificationUserCountInvalid";
    public const string DiscountPercentageValueOutOfRange = "GoTrack:Error.DiscountPercentageValueOutOfRange";
    public const string DiscountSpecificationNotFound = "GoTrack:Error.DiscountSpecificationNotFound";
    public const string DiscountPricingItemKeysRequired = "GoTrack:Error.DiscountPricingItemKeysRequired";
    public const string DiscountSpecificationRequestIdInvalid = "GoTrack:Error.DiscountSpecificationRequestIdInvalid";
    public const string RequestIdRequired = "GoTrack:Error.RequestIdRequired";
    public const string OwnerIdRequired = "GoTrack:Error.OwnerIdRequired";
    public const string SubscriptionPlanRequired = "GoTrack:Error.SubscriptionPlanRequired";
    public const string DevicesCountNegative = "GoTrack:Error.DevicesCountNegative";
    public const string TrackVehiclesCountNegative = "GoTrack:Error.TrackVehiclesCountNegative";
    public const string DevicesCountExceedsVehiclesCount = "GoTrack:Error.DevicesCountExceedsVehiclesCount";
    public const string VehicleCountNegative = "GoTrack:Error.VehicleCountNegative";
    public const string RemainingMonthsInvalid = "GoTrack:Error.RemainingMonthsInvalid";
    public const string SmsBundleIdRequired = "GoTrack:Error.SmsBundleIdRequired";
    public const string UserAlreadyHasPersonalSubscription = "GoTrack:Error.UserAlreadyHasPersonalSubscription";
    public const string UserAlreadyHasBusinessSubscription = "GoTrack:Error.UserAlreadyHasBusinessSubscription";
    public const string MaxRequestsExceeded = "GoTrack:Error.MaxRequestsExceeded";
    public const string PromoCodeRequired = "GoTrack:Error.PromoCodeRequired";
    public const string PromoCodeInvalidLength = "GoTrack:Error.PromoCodeInvalidLength";
    public const string PromoCodeAlreadyExists = "GoTrack:Error.PromoCodeAlreadyExists";
    public const string PromoCodeNotFound = "GoTrack:Error.PromoCodeNotFound";
    public const string PromoCodeNotActive = "GoTrack:Error.PromoCodeNotActive";
    public const string PromoCodeExpired = "GoTrack:Error.PromoCodeExpired";
    public const string PromoCodeEndDateMustBeInFuture = "GoTrack:Error.PromoCodeEndDateMustBeInFuture";
    public const string PromoCodeContainsWhiteSpace = "GoTrack:Error.PromoCodeContainsWhiteSpace";
    public const string DiscountFixedValueMustBePositive = "GoTrack:Error.DiscountFixedValueMustBePositive";
    public const string NotificationDaysMustBePositive = "GoTrack:Error.NotificationDaysMustBePositive";
    public const string GracePeriodMustBeNonNegative = "GoTrack:Error.GracePeriodMustBeNonNegative";
    public const string NoSmsBundleOrFinishedOfTrackAccountSubscription = "GoTrack:Error.NoSmsBundleOrFinishedOfTrackAccountSubscription";
    public const string SmsLowCountThresholdMustBePositive = "GoTrack:Error.SmsLowCountThresholdMustBePositive";
    public const string SubscriptionPlanNotValid = "GoTrack:Error.SubscriptionPlanNotValid";
    public const string UserNotEligibleForPremiumTrial = "GoTrack:Error.UserNotEligibleForPremiumTrial";
    public const string SubscriptionPlanExpired = "GoTrack:Error.SubscriptionPlanExpired";
    public const string SubscriptionPlanNotFound = "GoTrack:Error.SubscriptionPlanNotFound";
    public const string InvalidSubscriptionDurationRelation = "GoTrack:Error.InvalidSubscriptionDurationRelation";
    public const string PaymentDisabled = "GoTrack:Error.PaymentDisabled";
    public const string InvalidPolicyVersion = "GoTrack:Error.InvalidPolicyVersion";
    public const string PrivacyPolicyUpdateLocked = "GoTrack:Error.PrivacyPolicyUpdateLocked";
    public const string PaymentCreationFailed = "GoTrack:Error.PaymentCreationFailed";
    public const string VehicleAlreadyExistsInSystem = "GoTrack:Error.VehicleAlreadyExistsInSystem";
    public const string CurrencyException = "GoTrack:Error.CurrencyException";
    public const string InvalidLanguageCode = "GoTrack:Error.InvalidLanguageCode";
    public const string RequestsBeingProcessed = "GoTrack:Error.RequestsBeingProcessed";
}