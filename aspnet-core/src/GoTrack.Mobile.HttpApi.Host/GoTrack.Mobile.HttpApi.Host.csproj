<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\common.props" />

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>GoTrack.Mobile</RootNamespace>
        <PreserveCompilationReferences>true</PreserveCompilationReferences>
        <UserSecretsId>GoTrack-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
        <PackageReference Include="Volo.Abp.AspNetCore.MultiTenancy" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.Autofac" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.Swashbuckle" Version="8.3.0" />
        <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="8.3.0" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="3.3.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\GoTrack.EntityFrameworkCore\GoTrack.EntityFrameworkCore.csproj" />
        <ProjectReference Include="..\GoTrack.HttpApi\GoTrack.HttpApi.csproj" />
        <ProjectReference Include="..\GoTrack.Mobile.Application\GoTrack.Mobile.Application.csproj" />
        <ProjectReference Include="..\GoTrack.Mobile.HttpApi\GoTrack.Mobile.HttpApi.csproj" />
        <ProjectReference Include="..\MassTransitLib\MassTransitLib.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Logs\**" />
        <Content Remove="Logs\**" />
        <EmbeddedResource Remove="Logs\**" />
        <None Remove="Logs\**" />
        <Content Remove="SwaggerHelpers\Test.Json" />
    </ItemGroup>

</Project>
