using GoTrack.Providers;
using Microsoft.Extensions.Configuration;
using System;

namespace GoTrack.Mobile.Providers;

public class BaseUrlProvider : IBaseUrlProvider
{
    private readonly IConfiguration _configuration;

    public BaseUrlProvider(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GetBaseUrl()
    {
        var configurationSection = _configuration.GetSection("AuthServer");
        var baseUrl = configurationSection["Authority"];
        if (baseUrl is null)
            throw new ArgumentNullException(baseUrl);

        return baseUrl;
    }
}
