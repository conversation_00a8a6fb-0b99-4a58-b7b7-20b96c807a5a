using System;
using System.Diagnostics;
using GoTrack.Alerts.BaseChecker;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using MassTransit;
using MassTransit.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Warp10;
using Warp10.WarpLibs;
using Warp10Abstraction.WarpLibs;

namespace GoTrack.Alerts.CheckOverSpeed;

public class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureServices((hostContext, services) =>
            {
                services.AddScoped<IRepoService, RepoService>();

                services.AddScoped(x => 
                    new WarpService(
                        hostContext.Configuration["Warp10:Url"],
                        hostContext.Configuration["Warp10:ReadToken"],
                        hostContext.Configuration["Warp10:WriteToken"]
                    )
                );

                services.AddScoped<IWarpLib, WarpLib>();

                services.AddDbContext<GoTrackAlertCheckOverSpeedContext>(options => options
                    .UseMySql(hostContext.Configuration.GetConnectionString("DefaultConnection"),
                        ServerVersion.AutoDetect(
                            hostContext.Configuration.GetConnectionString("DefaultConnection")
                        )
                    )
                );

                services.AddScoped<IScopedProcessingService, ScopedProcessingService>();
                services.AddHostedService<ConsumeScopedServiceHostedService>();

                string serviceCode = hostContext.Configuration.GetValue<string>("InstanceCode");
                services.AddMassTransit(x =>
                {
                    x.AddConsumer<AlertsCrudConsumer>(x => { x.UseConcurrencyLimit(1); });
                    x.UsingRabbitMq((context, cfg) =>
                    {
                        cfg.Host(
                            hostContext.Configuration["RabbitMQ:Host"],
                            hostContext.Configuration["RabbitMQ:VirtualHost"],
                            h =>
                            {
                                h.Username(hostContext.Configuration["RabbitMQ:Username"]);
                                h.Password(hostContext.Configuration["RabbitMQ:Password"]);
                            });
                        
                        cfg.ReceiveEndpoint("over_speed_service_" + serviceCode, e =>
                        {
                            e.PrefetchCount = 1;
                            e.Bind<AlertCrudToService>(x =>
                            {
                                x.ExchangeType = "direct";
                                x.RoutingKey = serviceCode;
                            });
                            e.ConfigureConsumer<AlertsCrudConsumer>(context);
                            e.ConfigureConsumeTopology = false;
                        });
                        
                        cfg.ConfigureEndpoints(context);
                    });
                });
                var applicationName = hostContext.Configuration["OpenTelemetry:ApplicationName"];
                var endpointUri = hostContext.Configuration["OpenTelemetry:EndpointUri"];
                var openTelemetryIsEnable = bool.Parse(hostContext.Configuration["OpenTelemetry:EnableTracing"]);
                services.AddSingleton(_ => new ActivitySource(applicationName));
                if (openTelemetryIsEnable)
                {
                    services.AddOpenTelemetry()
                        .ConfigureResource(resource => resource.AddService(applicationName))
                        .WithTracing(tracing => 
                            tracing.SetResourceBuilder(
                                    ResourceBuilder.CreateDefault().AddService(applicationName)
                                )
                                .AddEntityFrameworkCoreInstrumentation(options => options.SetDbStatementForText = true)
                                .AddSource(applicationName)
                                .AddSource(DiagnosticHeaders.DefaultListenerName)
                                .AddOtlpExporter(x => x.Endpoint = new Uri(endpointUri))
                        );
                }
            });
}