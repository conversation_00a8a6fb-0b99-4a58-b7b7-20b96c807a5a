/* Your Global Styles */

:root .lpx-brand-logo {
    --lpx-logo: url('./images/logo/leptonx/logo-light.png');
    --lpx-logo-icon: url('./images/logo/leptonx/logo-light-thumbnail.png');
    --gray-light: #ededed;
}

body {
    background: rgb(255, 254, 255);
    background: linear-gradient(78deg, rgba(255, 254, 255, 1) 0%, rgba(219, 208, 230, 1) 10%, rgba(171, 160, 206, 1) 24%, rgba(116, 131, 191, 1) 46%, rgba(113, 163, 212, 1) 79%, rgba(113, 156, 204, 1) 100%);
}

.dropdown-menu.show {
    background: rgba(255, 255, 255, 0.75);
    transform: translate(4px, 23px) !important;
    max-height: calc(100vh - 32px);
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 7px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgb(255, 254, 255);
        background: linear-gradient(180deg, rgba(255, 254, 255, 1) 0%, rgba(219, 208, 230, 1) 24%, rgba(171, 160, 206, 1) 49%, rgba(116, 131, 191, 1) 70%, rgba(113, 163, 212, 1) 85%, rgba(113, 156, 204, 1) 100%);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
        background: var(--gray-light);
    }

    &::-webkit-scrollbar-thumb:hover {
        background: rgb(255, 254, 255);
        background: linear-gradient(180deg, rgba(255, 254, 255, 1) 0%, rgba(219, 208, 230, 1) 24%, rgba(171, 160, 206, 1) 49%, rgba(116, 131, 191, 1) 70%, rgba(113, 163, 212, 1) 85%, rgba(113, 156, 204, 1) 100%);
    }
}

.Login-background {
    width: 50%;
    height: 85vh;
    background-color: rgba(255, 255, 255, 0.33);
    border-radius: 20px;
    align-items: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.abp-account-container-holder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-btn-style {
    background-color: #7483bf;
    border-color: #7483bf;
    width: 50%
}

.login-btn-style:hover {
    background-color: #6678be;
    border-color: #6678be;
    width: 50%
}

.login-form-style {
    width: 85%;
}

.form-check-label {
    color: white;
}

.input-style {
    & input {
        background-color: transparent;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        border-radius: unset;
        color: white;
    }

    & input:hover {
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        border-radius: unset;
        color: white;
    }

    & input:focus {
        background-color: transparent;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        border-radius: unset;
        color: white;
    }

    & label {
        color: white;
    }

    & label:after {
        color: white !important;
        background-color: transparent !important;
    }

    .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-control-plaintext ~ label, .form-floating > .form-select ~ label {
        color: white;
        opacity: unset;
    }
}

.form-check-input:checked {
    border-color: #6678be !important;
    background-color: #6678be !important;
}

[dir="rtl"] {
    .dropdown-menu.show {
        transform: translate(-4px, 23px) !important;
    }

    & label:after {
        color: white !important;
        background-color: transparent !important;
    }

    .form-control:focus {
        border-bottom-color: white;
        -webkit-box-shadow: none;
       color: white;
    }

    .input-style:focus-within label {
        color: white;
    }

    .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-control-plaintext ~ label, .form-floating > .form-select ~ label {
        color: white;
    }
}