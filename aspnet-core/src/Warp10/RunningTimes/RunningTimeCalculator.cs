#nullable enable
using Warp10.Processors;
using Warp10.TimePeriods;
using Warp10Abstraction.Sensors;

namespace Warp10.RunningTimes
{
    public class RunningTimeCalculator : IRunningTimeCalculator
    {
        private readonly ISensorRepository _sensorRepository;
        private readonly ITimePeriodDissector _timePeriodDissector;
        public RunningTimeCalculator(ISensorRepository sensorRepository, ITimePeriodDissector timePeriodDissector)
        {
            _sensorRepository = sensorRepository;
            _timePeriodDissector = timePeriodDissector;
        }

        public async Task<List<TimePeriod>> GetAllAsync(string imei, DateTime from, DateTime to, RunningTimeCalculatorOptions _options)
        {
            var result = new List<TimePeriod>();

            var rangeCollectionToIntersect = new Queue<List<TimePeriod>>();

            if (_options.UseVibrationSensor)
            {
                var vibrationGts = await
                    _sensorRepository.GetAsync(imei, Sensor.Vibration, from - _options.VibrationForwardFillResamplingLimit, to);
                if (vibrationGts is null)
                    return result;
                var orderAsc = _timePeriodDissector.ParseOrderAsc(vibrationGts.Values);
                List<TimePeriod> vibrationResult = _timePeriodDissector.DissectByMinValue(orderAsc,
                    _options.VibrationPerMinuteThreshold, _options.VibrationForwardFillResamplingLimit);

                vibrationResult = _timePeriodDissector.Trim(vibrationResult, from, to);
                if (!vibrationResult.Any())
                    return result;
                rangeCollectionToIntersect.Enqueue(vibrationResult);
                from = vibrationResult.First().From.UtcDateTime;
                to = vibrationResult.Last().To!.Value.UtcDateTime;
            }

            if (_options.UseIgnitionSensor)
            {
                var ignitionGts = await
                    _sensorRepository.GetAsync(imei, Sensor.Ignition, from - _options.IgnitionForwardFillResamplingLimit , to);
                if (ignitionGts is null)
                    return result;
                var orderAsc = _timePeriodDissector.ParseOrderAsc(ignitionGts.Values);
                List<TimePeriod> ignitionResult = _timePeriodDissector.DissectByMinValue(orderAsc, 1, _options.IgnitionForwardFillResamplingLimit);
                ignitionResult = _timePeriodDissector.Trim(ignitionResult, from, to);
                if (!ignitionResult.Any())
                    return result;
                rangeCollectionToIntersect.Enqueue(ignitionResult);
                from = ignitionResult.First().From.UtcDateTime;
                to = ignitionResult.Last().To!.Value.UtcDateTime; 
            }

            if (_options.UseExternalVoltageSensor)
            {
                var voltageGts= await
                    _sensorRepository.GetAsync(imei, Sensor.ExternalVoltage, from - _options.VoltageForwardFillResamplingLimit, to);
                if (voltageGts is null)
                    return result;
                var orderAsc = _timePeriodDissector.ParseOrderAsc(voltageGts.Values);
                List<TimePeriod> voltageResult = _timePeriodDissector.DissectByMinValue(orderAsc,
                    _options.ExternalVoltageThreshold, _options.VoltageForwardFillResamplingLimit);
                voltageResult = _timePeriodDissector.Trim(voltageResult, from, to);
                if (!voltageResult.Any())
                    return result;
                rangeCollectionToIntersect.Enqueue(voltageResult);

            }
            
            if (!rangeCollectionToIntersect.Any())
                return result;

            result = rangeCollectionToIntersect.Dequeue();

            while (rangeCollectionToIntersect.Any())
            {
                result = FindIntersections(result, rangeCollectionToIntersect.Dequeue());
            }
            
            return result;
        }
        
        public async Task<List<TimePeriod>> GetOnlyMovingAsync(string imei, DateTime from, DateTime to, RunningTimeCalculatorOptions _options)
        {
            var speedGts = await _sensorRepository.GetAsync(imei, Sensor.Speed, from - _options.SpeedForwardFillResamplingLimit, to);
            if (speedGts is null)
                return new List<TimePeriod>();
            
            var orderAsc = _timePeriodDissector.ParseOrderAsc(speedGts.Values);
            orderAsc = _timePeriodDissector.FilterOutliers(orderAsc, _options.SpeedForwardFillResamplingLimit);
            List<TimePeriod> result = _timePeriodDissector.DissectByMinValue(orderAsc, _options.SpeedThreshold, _options.SpeedForwardFillResamplingLimit);
            return _timePeriodDissector.Trim(result, from, to);
        }

        public async Task<RunningTime> GetDetailedAsync(string imei, DateTime from, DateTime to, RunningTimeCalculatorOptions _options)
        {
            var result = new RunningTime
            {
                Moving = new List<TimePeriod>(),
                Stationery = new List<TimePeriod>()
            };
            
            result.Moving = await GetOnlyMovingAsync(imei, from, to, _options);
            result.Stationery = await GetAllAsync(imei, from, to, _options);
            result.Stationery = Subtract(result.Stationery, result.Moving);

            return result;
        }
        
        private List<TimePeriod> FindIntersections(List<TimePeriod> list1, List<TimePeriod> list2)
        {
            List<TimePeriod> result = new List<TimePeriod>();

            if (!list1.Any() || !list2.Any())
                return result;
            
            //TODO optimize this and move it 
            foreach (TimePeriod rt1 in list1)
            {
                foreach (var rt2 in list2)
                {
                    var intersectingRunningTime = rt1.GetIntersection(rt2);
                    if (intersectingRunningTime is not null)
                        result.Add(intersectingRunningTime);
                }
            }

            return result;
            
        }
        //TODO move list time period subtraction into own service
        private List<TimePeriod> Subtract(List<TimePeriod> minuend, List<TimePeriod> subtrahend)
        {
            if (!subtrahend.Any())
                return minuend;
            if (!minuend.Any())
                return minuend;
            TimePeriod fullRange = new TimePeriod(minuend.First().From, minuend.Last().To.Value);
            var invert = fullRange.Subtract(subtrahend);
            return this.FindIntersections(minuend, invert);
            
        }
        
    }
}