namespace Warp10.RunningTimes
{
    public class RunningTimeCalculatorOptions
    {
        public static readonly decimal DefaultVibrationPerMinuteThreshold = 50;
        public static readonly decimal DefaultSpeedThreshold = 1;
        public static readonly decimal DefaultExternalVoltageThreshold = 13;
        public static readonly TimeSpan DefaultSpeedForwardFillResamplingLimit = TimeSpan.FromMinutes(1);
        public static readonly TimeSpan DefaultVibrationForwardFillResamplingLimit = TimeSpan.FromMinutes(3);
        public static readonly TimeSpan DefaultVoltageForwardFillResamplingLimit = TimeSpan.FromMinutes(15);
        public static readonly TimeSpan DefaultIgnitionForwardFillResamplingLimit = TimeSpan.FromHours(48);

        public RunningTimeCalculatorOptions()
        {
            UseVibrationSensor = true;
            UseIgnitionSensor = true;
            UseExternalVoltageSensor = true;

            SpeedThreshold = DefaultSpeedThreshold;
            VibrationPerMinuteThreshold = DefaultVibrationPerMinuteThreshold;
            ExternalVoltageThreshold = DefaultExternalVoltageThreshold;

            SpeedForwardFillResamplingLimit = DefaultSpeedForwardFillResamplingLimit;
            VibrationForwardFillResamplingLimit = DefaultVibrationForwardFillResamplingLimit;
            IgnitionForwardFillResamplingLimit = DefaultIgnitionForwardFillResamplingLimit;
            VoltageForwardFillResamplingLimit = DefaultVoltageForwardFillResamplingLimit;
        }

        public bool UseVibrationSensor { get; set; }
        public bool UseIgnitionSensor { get; set; }
        public bool UseExternalVoltageSensor { get; set; }
        private decimal _vibrationPerMinuteThreshold;

        public decimal VibrationPerMinuteThreshold
        {
            get => _vibrationPerMinuteThreshold;
            set
            {
                ValidatePositive(value);
                _vibrationPerMinuteThreshold = value;
            }
        }

        private decimal _externalVoltageThreshold;

        public decimal ExternalVoltageThreshold
        {
            get => _externalVoltageThreshold;
            set
            {
                ValidatePositive(value);
                _externalVoltageThreshold = value;
            }
        }

        private decimal _speedThreshold;

        public decimal SpeedThreshold
        {
            get => _speedThreshold;
            set
            {
                ValidatePositive(value);
                _speedThreshold = value;
            }
        }

        private TimeSpan _speedForwardFillResamplingLimit;

        public TimeSpan SpeedForwardFillResamplingLimit
        {
            get => _speedForwardFillResamplingLimit;
            set
            {
                ValidatePositive(value);
                _speedForwardFillResamplingLimit = value;
            }
        }

        private TimeSpan _vibrationForwardFillResamplingLimit;
        public TimeSpan VibrationForwardFillResamplingLimit
        {
            get => _vibrationForwardFillResamplingLimit;
            set
            {
                ValidatePositive(value);
                _vibrationForwardFillResamplingLimit = value;
            }
        }

        private TimeSpan _voltageForwardFillResamplingLimit;
        public TimeSpan VoltageForwardFillResamplingLimit
        {
            get => _voltageForwardFillResamplingLimit;
            set
            {
                ValidatePositive(value);
                _voltageForwardFillResamplingLimit = value;
            }
        }

        private TimeSpan _ignitionForwardFillResamplingLimit;

        public TimeSpan IgnitionForwardFillResamplingLimit
        {
            get => _ignitionForwardFillResamplingLimit;
            set
            {
                ValidatePositive(value);
                _ignitionForwardFillResamplingLimit = value;
            }
        }

        private void ValidatePositive(decimal value)
        {
            if (value <= 0)
                throw new ArgumentOutOfRangeException();
        }
        
        private void ValidatePositive(TimeSpan value)
        {
            if (value <= TimeSpan.Zero)
                throw new ArgumentOutOfRangeException();
        }
    }
}