using Warp10Abstraction;
using Warp10Abstraction.Models;
using static System.Formats.Asn1.AsnWriter;

namespace Warp10.Scripts
{
    public static class AlertsScripts
    {
        /// <param name="polygonHHcode">List of HHCodes strings "cb4e3e142" +! ..." </param>
        /// <param name="fromDate">must be in unix timestamp format </param>
        /// <param name="toDate">must be in unix timestamp format </param>
        public static async Task<ViolationResult[]> CheckImeisInOutArea(this WarpService warpService, string[] deviceImei,
            string polygonHHcode, bool isIn,
            string fromDate, string toDate, bool checkAllExceptThose = false)
        {
            string imeiFilter = WarpHelper.GetMultiValuesLabelFilter(deviceImei, checkAllExceptThose);
            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + imei<PERSON>ilter + @"' 'imei' STORE
                            " + (isIn ? "true" : "false") + @" 'isIn' STORE

                            // generate geo zone
                            [] "+ polygonHHcode +" " + @"->GEOSHAPE 'zone' STORE

                            // Get the speed data in the selected period
                            [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                            [ SWAP $zone mapper.geo.fence 0 0 0 ] MAP
                            []
                            SWAP
                            <%
                                DUP LABELS 'imei' GET 'im' STORE
                                DUP TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE
                                <% $isIn == %>
                                @ttwr/SPLITONVALUECHANGE
                                DUP SIZE 0 ==
                                <% %> <% 0 GET %> IFTE
                                'result' STORE
                                { 'Imei' $im 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp 'ViolatedIntervals' $result  }
                                +!
                            %> FOREACH
                            'all_res' STORE
                            { 'Result' true 'Data' $all_res }";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ViolationResult[]>>(script));
        }

        public static async Task<ViolationResult[]> CheckImeisOverSpeed(this WarpService warpService, string[] deviceImei,
            int speed,
            string fromDate, string toDate)
        {
            string imeiFilter = WarpHelper.GetMultiValuesLabelFilter(deviceImei);
            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + imeiFilter + @"' 'imei' STORE "
                            + speed + @" 'speedLimit' STORE "
                            + @"

                            // Get the speed data in the selected period
                            [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                            []
                            SWAP
                            <%
                                DUP LABELS 'imei' GET 'im' STORE
                                DUP TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE
                                <% $speedLimit > %>
                                @ttwr/SPLITONVALUECHANGE
                                DUP SIZE 0 ==
                                <% %> <% 0 GET %> IFTE
                                'result' STORE
                                { 'Imei' $im 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp 'ViolatedIntervals' $result  }
                                +!
                            %> FOREACH
                            'all_res' STORE
                            { 'Result' true 'Data' $all_res }";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ViolationResult[]>>(script));
        }

        public static async Task<ViolationResult> CheckStoppingPointsIfAllowed(this WarpService warpService,
            string deviceImei, int stopped_min_time_seconds,
            int stopped_max_speed_kmh, int stopped_max_mean_speed_kmh, int stopped_max_radius_meters, string[] polygons,
            string fromDate, string toDate)
        {
            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + deviceImei + @"' 'imei' STORE "
                            + stopped_min_time_seconds + @" 'stopped_min_time_seconds' STORE "
                            + stopped_max_speed_kmh + @" 'stopped_max_speed_km/h' STORE "
                            + stopped_max_mean_speed_kmh + @" 'stopped_max_mean_speed_km/h' STORE "
                            + stopped_max_radius_meters + @" 'stopped_max_radius_meters' STORE "
                            + "[ " + String.Join(' ', polygons) + " ] 'allowedZonesPolygions' STORE "
                            + @"
                            <% 
                                [ ] 'allowedZonesWkts' STORE

                                $allowedZonesPolygions 
                                <%
                                    'ele' STORE
                                    $allowedZonesWkts
                                    'POLYGON ((' $ele TOSTRING + '))' + 0.01 true GEO.WKT 
                                    +!
                                    DROP
                                %>
                                FOREACH

                                [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                                'all_data' STORE
                                <% $all_data SIZE 0 == %>
                                <%
                                    { 'Result' true 'Data' { 'ViolatedIntervals' [] 'FirstTimestamp' 0 'LastTimestamp' 0 } }  
                                %>
                                <%
                                    $all_data TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE

                                    <% 3.6 / %> 'km/h' STORE

                                    $all_data
                                    {
                                    'stopped.min.time'  $stopped_min_time_seconds s
                                    'stopped.max.speed' $stopped_max_speed_km/h @km/h
                                    'stopped.max.radius' $stopped_max_radius_meters
                                    'stopped.max.mean.speed' $stopped_max_mean_speed_km/h @km/h
                                    'stopped.split' true
                                    'label.split.number' 'split'
                                    'label.split.type' 'reason'
                                    }
                                    MOTIONSPLIT
                                    0 GET
                                    [
                                    SWAP
                                    []
                                    { 'reason' '=stopped' }
                                    filter.bylabels
                                    ] FILTER

                                    $allowedZonesWkts
                                    <%
                                        'ele' STORE
                                        [ SWAP $ele mapper.geo.outside 0 0 0 ] MAP
                                    %>
                                    FOREACH

                                    [ SWAP [] 1 99999999 filter.bysize ] FILTER 'result' STORE

                                    { 'Result' true 'Data' { 'ViolatedIntervals' $result 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp } }  
                                %> 
                                IFTE
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ViolationResult>>(script));
        }


        public static async Task<ViolationResult> CheckStopInZone(this WarpService warpService,
            string deviceImei, int stopped_min_time_seconds,
            int stopped_max_speed_kmh, int stopped_max_mean_speed_kmh, int stopped_max_radius_meters, string polygon,
            string fromDate, string toDate)
        {
            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + deviceImei + @"' 'imei' STORE "
                            + stopped_min_time_seconds + @" 'stopped_min_time_seconds' STORE "
                            + stopped_max_speed_kmh + @" 'stopped_max_speed_km/h' STORE "
                            + stopped_max_mean_speed_kmh + @" 'stopped_max_mean_speed_km/h' STORE "
                            + stopped_max_radius_meters + @" 'stopped_max_radius_meters' STORE "
                            + "'" + polygon + @"' 'polygon' STORE "
                            + @"
                            'POLYGON ((' $polygon TOSTRING + '))' + 0.01 true GEO.WKT 
                            'zone' STORE
                            $startDate TOLONG $stopped_min_time_seconds 2 * 1000 * - TOSTRING 'startDate' STORE
                            <% 

                                [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                                'all_data' STORE
                                <% $all_data SIZE 0 == %>
                                <%
                                    { 'Result' true 'Data' { 'ViolatedIntervals' [] 'FirstTimestamp' 0 'LastTimestamp' 0 } }  
                                %>
                                <%
                                    $all_data TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE

                                    <% 3.6 / %> 'km/h' STORE

                                    $all_data
                                    {
                                    'stopped.min.time'  $stopped_min_time_seconds s
                                    'stopped.max.speed' $stopped_max_speed_km/h @km/h
                                    'stopped.max.radius' $stopped_max_radius_meters
                                    'stopped.max.mean.speed' $stopped_max_mean_speed_km/h @km/h
                                    'stopped.split' true
                                    'label.split.number' 'split'
                                    'label.split.type' 'reason'
                                    }
                                    MOTIONSPLIT
                                    0 GET
                                    [
                                    SWAP
                                    []
                                    { 'reason' '=stopped' }
                                    filter.bylabels
                                    ] FILTER

                                    [ SWAP $zone mapper.geo.within 0 0 0 ] MAP
                                    [ SWAP [] 1 99999999 filter.bysize ] FILTER 
                                    'result' STORE
                                    { 'Result' true 'Data' { 'ViolatedIntervals' $result 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp } }  
                                %> 
                                IFTE
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ViolationResult>>(script));
        }

        public static async Task<ViolationResult[]> CheckImeisExternalPowerCutOffAsync(this WarpService warpService, string[] deviceImeis, string fromDate, string toDate)
        {
            string imeiFilter = WarpHelper.GetMultiValuesLabelFilter(deviceImeis);

            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + imeiFilter + @"' 'imei' STORE "
                            + @"

                            // Get the external power cut off data in the selected period
                            [ $readToken 'external_power_cut_off' { 'imei' $imei } $startDate $endDate ] FETCH
                            []
                            SWAP
                            <%
                                DUP LABELS 'imei' GET 'im' STORE
                                DUP TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE
                                <% 1 == %>
                                @ttwr/SPLITONVALUECHANGE
                                DUP SIZE 0 ==
                                <% %> <% 0 GET %> IFTE
                                'result' STORE
                                { 'Imei' $im 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp 'ViolatedIntervals' $result  }
                                +!
                            %> FOREACH
                            'all_res' STORE
                            { 'Result' true 'Data' $all_res }";

            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ViolationResult[]>>(script));
        }
    }
}