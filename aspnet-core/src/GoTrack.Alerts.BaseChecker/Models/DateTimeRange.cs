using System;

namespace GoTrack.Alerts.BaseChecker.Models;

public struct DateTimeRange
{
    #region Construction

    public DateTimeRange(DateTime start, DateTime end)
    {
        if (start > end)
        {
            throw new Exception("Invalid range edges.");
        }

        Start = start;
        End = end;
    }

    #endregion

    #region Properties

    public DateTime Start { get; private set; }

    public DateTime End { get; private set; }

    #endregion

    #region Operators

    public static bool operator ==(DateTimeRange range1, DateTimeRange range2)
    {
        return range1.Equals(range2);
    }

    public static bool operator !=(DateTimeRange range1, DateTimeRange range2)
    {
        return !(range1 == range2);
    }

    public override bool Equals(object obj)
    {
        if (obj is DateTimeRange range2)
            return Start == range2.Start && End == range2.End;

        return base.Equals(obj);
    }

    #endregion

    #region Querying

    public bool Intersects(DateTimeRange range)
    {
        var type = GetIntersectionType(range);
        return type != IntersectionType.None;
    }

    public bool IsInRange(DateTime date)
    {
        return date >= Start && date <= End;
    }

    public IntersectionType GetIntersectionType(DateTimeRange range)
    {
        if (this == range)
            return IntersectionType.RangesEqauled;

        if (IsInRange(range.Start) && IsInRange(range.End))
            return IntersectionType.ContainedInRange;

        if (IsInRange(range.Start))
            return IntersectionType.StartsInRange;

        if (IsInRange(range.End))
            return IntersectionType.EndsInRange;

        if (range.IsInRange(Start) && range.IsInRange(End))
            return IntersectionType.ContainsRange;

        return IntersectionType.None;
    }

    public DateTimeRange GetIntersection(DateTimeRange range)
    {
        var type = GetIntersectionType(range);

        if (type == IntersectionType.RangesEqauled || type == IntersectionType.ContainedInRange)
            return range;

        if (type == IntersectionType.StartsInRange)
            return new DateTimeRange(range.Start, End);

        if (type == IntersectionType.EndsInRange)
            return new DateTimeRange(Start, range.End);

        if (type == IntersectionType.ContainsRange)
            return this;

        return default;
    }

    #endregion


    public override string ToString()
    {
        return Start + " - " + End;
    }

    public override int GetHashCode() => base.GetHashCode();
}

public enum IntersectionType
{
    /// <summary>
    /// No Intersection
    /// </summary>
    None = -1,

    /// <summary>
    /// Given range ends inside the range
    /// </summary>
    EndsInRange,

    /// <summary>
    /// Given range starts inside the range
    /// </summary>
    StartsInRange,

    /// <summary>
    /// Both ranges are equaled
    /// </summary>
    RangesEqauled,

    /// <summary>
    /// Given range contained in the range
    /// </summary>
    ContainedInRange,

    /// <summary>
    /// Given range contains the range
    /// </summary>
    ContainsRange,
}