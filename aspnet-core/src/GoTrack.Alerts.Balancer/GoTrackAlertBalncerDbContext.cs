using GoTrack.Alerts.Balancer.Models;
using Microsoft.EntityFrameworkCore;

namespace GoTrack.Alerts.Balancer;

public class GoTrackAlertBalncerDbContext : DbContext
{
    public GoTrackAlertBalncerDbContext()
    {

    }

    public GoTrackAlertBalncerDbContext(DbContextOptions<GoTrackAlertBalncerDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AlertList> AlertLists { get; set; }
    public virtual DbSet<FreeAlertList> FreeAlertLists { get; set; }
    public virtual DbSet<Round> Rounds { get; set; }
    public virtual DbSet<Server> Servers { get; set; }
    public virtual DbSet<ServerService> ServerServices { get; set; }
    public virtual DbSet<Service> Services { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            optionsBuilder.UseMySql("name=DefaultConnection", ServerVersion.Parse("5.7.36-mysql"));
        }
    }
}
