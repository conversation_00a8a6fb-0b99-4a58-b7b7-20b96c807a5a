{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=GoTrack_Alerts_Balancer;Uid=root;Pwd=;TreatTinyAsBoolean=true;Connection Timeout=300;default command timeout=300"}, "RabbitMQ": {"Host": "*************", "VirtualHost": "/", "Username": "guest", "Password": "guest"}}