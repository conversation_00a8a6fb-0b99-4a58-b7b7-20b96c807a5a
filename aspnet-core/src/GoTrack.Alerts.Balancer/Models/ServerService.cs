using System;

namespace GoTrack.Alerts.Balancer.Models;

public class ServerService
{
    public decimal ServerServiceId { get; set; }
    public decimal ServiceId { get; set; }
    public decimal ServerId { get; set; }
    public string InstanceCode { get; set; }
    public string Url { get; set; }
    public decimal? Port { get; set; }
    public string StartCommand { get; set; }
    public string StopCommand { get; set; }
    public string RestartCommand { get; set; }
    public decimal? AlertsCount { get; set; }
    public DateTime? LastUpTime { get; set; }
    public DateTime? LastDownTime { get; set; }
    public string StatusCode { get; set; }
    public DateTime? StatusDate { get; set; }

    public virtual Server Server { get; set; }
    public virtual Service Service { get; set; }
}