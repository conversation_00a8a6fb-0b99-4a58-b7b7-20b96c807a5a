using System.Collections.Generic;

namespace GoTrack.Alerts.Balancer.Models;

public class Server
{
    public Server()
    {
        AlertLists = new HashSet<AlertList>();
        Rounds = new HashSet<Round>();
        ServerServices = new HashSet<ServerService>();
    }

    public decimal ServerId { get; set; }
    public string Ip { get; set; }
    public ulong? IsVertual { get; set; }
    public string Brand { get; set; }
    public string Model { get; set; }
    public string Description { get; set; }

    public virtual ICollection<AlertList> AlertLists { get; set; }
    public virtual ICollection<Round> Rounds { get; set; }
    public virtual ICollection<ServerService> ServerServices { get; set; }
}