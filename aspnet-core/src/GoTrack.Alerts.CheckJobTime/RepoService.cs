using GoTrack.Alerts.BaseChecker;
using GoTrack.Alerts.BaseChecker.Models;
using GoTrack.Alerts.CheckJobTime.Models;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using System;
using System.Linq;
using System.Threading.Tasks;
using Warp10Abstraction;

namespace GoTrack.Alerts.CheckJobTime;

public class RepoService : IRepoService
{
    private readonly GoTrackAlertCheckJobTimeContext _context;

    public RepoService(GoTrackAlertCheckJobTimeContext context)
    {
        _context = context;
    }

    public async Task DeleteAlert(AlertBase alertBase)
    {
        _context.AlertLists.Remove((AlertList)alertBase);

        await _context.SaveChangesAsync();
    }

    public AlertBase GetAlert(Guid evId)
    {
        return _context.AlertLists.FirstOrDefault(x => x.Id == evId);
    }

    public AlertBase[] GetAlertsByIds(string[] ids)
    {
        return [.. _context.AlertLists.Where(x => ids.Contains(x.Id.ToString()))];
    }

    public GroupedAlertListBase[] GetGroupedAlertsList(int pageNumber, int pageSize)
    {
        return [.. _context.GroupedAlertLists
            .Skip(pageNumber * pageSize)
            .Take(pageSize)];
    }

    public int GetGroupedAlertsListCount()
    {
        return _context.GroupedAlertLists.Count();
    }

    public async Task ParseAndSaveAlert(AlertCrudToService alert)
    {
        AlertCrud alertCrud = alert.AlertCrud;

        var alertList = (AlertList)GetAlert(alertCrud.Id);

        if (alertCrud.CrudType == CrudType.Update && alertList is null)
        {
            throw new Exception("Update operation to non existing alert, AlertId: " + alertCrud.Id);
        }

        if (alertCrud.CrudType == CrudType.Add)
        {
            alertList = new AlertList()
            {
                Id = alertCrud.Id,
                AffectiveFrom = alertCrud.AffectiveFrom,
                LastCheckedAt = WarpHelper.ConvertDate(alertCrud.AffectiveFrom).ToString(),
            };
        }

        if (alertCrud is JobTimeAlertCrud jobTimeAlert)
        {
            alertList.Imei = jobTimeAlert.Imei;
            alertList.Name = jobTimeAlert.Name;
            alertList.StartTime = jobTimeAlert.StartTime;
            alertList.EndTime = jobTimeAlert.EndTime;
            alertList.DaysOfWeek = jobTimeAlert.DaysOfWeek;
        }

        if (alertCrud.CrudType == CrudType.Add)
        {
            _context.AlertLists.Add(alertList);
        }

        if (alertCrud.CrudType == CrudType.Update)
        {
            _context.AlertLists.Update(alertList);
        }

        await _context.SaveChangesAsync();
    }

    public void SaveUpdatedAlert()
    {
        _context.SaveChanges();
    }
}