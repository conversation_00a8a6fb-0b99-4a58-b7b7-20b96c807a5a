using GoTrack.VehicleGroups;
using System;

namespace GoTrack.TrackableEntities;

public class VehicleGroupTrackableEntityAssociation : TrackableEntityAssociation
{
    public Guid VehicleGroupId { get; private set; }

    #region Navigation
    
    public VehicleGroup VehicleGroup { get; private set; }
    
    #endregion

    private VehicleGroupTrackableEntityAssociation() { }

    public VehicleGroupTrackableEntityAssociation(Guid id, Guid vehicleGroupId) : base(id, TrackableEntityType.VehicleGroup) 
    {
        VehicleGroupId = vehicleGroupId;
    }
}
