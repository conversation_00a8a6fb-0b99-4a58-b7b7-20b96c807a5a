using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Devices;

public class DeviceStatusLog : FullAuditedEntity<Guid>
{
    public DeviceStatus Status { get; private set; }

    public DateTime Date { get; private set; }
    
    public Guid DeviceId { get; private set; }

    #region Navigation
    
    public Device Device { get; private set; }
    
    #endregion

    private DeviceStatusLog()
    {
    }

    public DeviceStatusLog(Guid id, Guid deviceId, DeviceStatus status) : base(id)
    {
        DeviceId = deviceId;
        Status = status;
        Date = DateTime.UtcNow;
    }
}