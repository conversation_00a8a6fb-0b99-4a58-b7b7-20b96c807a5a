using FirebaseAdmin.Messaging;
using GoTrack.BackgroundJobs.JobArguments;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

namespace GoTrack.BackgroundJobs.Jobs;

public class ResendPushNotificationJob : AsyncBackgroundJob<ResendPushNotificationArgs>, ITransientDependency
{
    public override async Task ExecuteAsync(ResendPushNotificationArgs args)
    {
        var message = new MulticastMessage
        {
            Tokens = args.FcmTokens,
            Notification = new Notification { Title = args.Title, Body = args.Body },
            Data = args.Data ?? []
        };

        var response = await FirebaseMessaging.DefaultInstance.SendEachForMulticastAsync(message);

        for (int i = 0; i < response.Responses.Count; i++)
        {
            if (!response.Responses[i].IsSuccess)
                throw response.Responses[i].Exception;
        }
    }
}