using Volo.Abp.Settings;

namespace GoTrack.Settings;

public class GoTrackSettingDefinitionProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        context.Add(
            new SettingDefinition(
                GoTrackSettings.GracePeriod,
                GoTrackSettings.GracePeriodDefaultValue.ToString(),
                isVisibleToClients: true,
                isEncrypted: false)
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.NotificationDays,
                string.Join(",", GoTrackSettings.NotificationDaysDefaultValue.ToArray()),
                isVisibleToClients: true,
                isEncrypted: false)
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.SmsLowCountThreshold,
                GoTrackSettings.SmsLowCountThresholdDefaultValue.ToString(),
                isVisibleToClients: true,
                isEncrypted: false)
        );
        
        context.Add(
            new SettingDefinition(
                GoTrackSettings.PersonalRequestFatoraPayEnabled,
                GoTrackSettings.PersonalRequestPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.BusinessRequestFatoraPayEnabled,
                GoTrackSettings.BusinessRequestPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.AddVehiclesRequestFatoraPayEnabled,
                GoTrackSettings.AddVehiclesRequestPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.IncreaseUserCountRequestFatoraPayEnabled,
                GoTrackSettings.IncreaseUserCountRequestPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.RenewTrackAccountSubscriptionFatoraPayEnabled,
                GoTrackSettings.RenewTrackAccountSubscriptionPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettings.SmsBundleRenewalRequestFatoraPayEnabled,
                GoTrackSettings.SmsBundleRenewalRequestPayEnabledDefault.ToString(),
                isVisibleToClients: true
            )
        );
    }
}
