using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.FCMDevices;

public class FcmDeviceManager : DomainService
{
    private const int InactiveDeviceRetentionMonths = 2;
    private readonly IRepository<FcmDevice, Guid> _repository;

    public FcmDeviceManager(IRepository<FcmDevice, Guid> repository)
    {
        _repository = repository;
    }

    public async Task RegisterDeviceAsync(string fcmToken, DeviceType deviceType,string deviceId, Guid? userId = null,
        string? name = null)
    {
        var existingDevice =
            await _repository.FirstOrDefaultAsync(d => d.DeviceId == deviceId);

        if (existingDevice == null)
        {
            var newDevice = new FcmDevice(fcmToken, deviceType, userId, name, deviceId);
            await _repository.InsertAsync(newDevice);
            return;
        }

        var isFcmTokenExist = await _repository.AnyAsync(x => x.FcmToken == fcmToken);
        if (existingDevice.FcmToken != fcmToken && isFcmTokenExist)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.FcmTokenMustBeUnique);
        }

        existingDevice.SetUserId(userId);
        existingDevice.SetName(name);
        existingDevice.SetFcmToken(fcmToken);
        existingDevice.SetDeviceId(deviceId);

        await _repository.UpdateAsync(existingDevice);
    }

    public async Task DeactivateDeviceAsync(Guid deviceId)
    {
        var device = await _repository.GetAsync(deviceId);
        
        device.SetIsActive(false);
        
        await _repository.UpdateAsync(device);
    }

    public async Task DeactivateDeviceAsync(string token)
    {
        var device = await _repository.GetAsync(x => x.FcmToken == token);
        
        device.SetIsActive(false);

        await _repository.UpdateAsync(device);
    }

    public async Task<List<FcmDevice>> GetUserDevicesAsync(Guid userId)
    {
        return await _repository.GetListAsync(d => d.UserId == userId && d.IsActive);
    }

    public async Task CleanupInactiveDevicesAsync()
    {
        await _repository.DeleteAsync(d =>
            !d.IsActive &&
            d.LastModificationTime < Clock.Now.AddMonths(InactiveDeviceRetentionMonths));
    }
    
    public async Task DeactivateAllDeviceOfUserAsync(Guid userId)
    {
        var deviceQuery = (await _repository.GetQueryableAsync()).Where(fcmDevice => fcmDevice.IsActive && fcmDevice.UserId == userId);
        var devices = await AsyncExecuter.ToListAsync(deviceQuery);

        foreach (var device in devices)
        {
            device.SetIsActive(false);
        }

        await _repository.UpdateManyAsync(devices);
    }
}