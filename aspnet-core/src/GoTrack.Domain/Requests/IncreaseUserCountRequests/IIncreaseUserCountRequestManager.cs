using System;
using System.Threading.Tasks;

namespace GoTrack.Requests.IncreaseUserCountRequests;

public interface IIncreaseUserCountRequestManager : IBillableRequestManager<IncreaseUserCountRequest>
{
    Task MarkAsCompleted(Guid requestId);
    Task<IncreaseUserCountRequest> CreateAsync(Guid ownerId, Guid subscriptionId, int observersCount,
        Guid trackAccountId);
    Task<IncreaseUserCountRequest> CreateTempAsync(Guid ownerId, Guid subscriptionId, int observersCount,
        Guid trackAccountId);
}