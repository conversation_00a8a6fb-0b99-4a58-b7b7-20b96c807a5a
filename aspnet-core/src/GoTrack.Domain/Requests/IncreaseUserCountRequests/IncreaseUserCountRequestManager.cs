using System;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests.IncreaseUserCountRequests;

public class IncreaseUserCountRequestManager : DomainService, IIncreaseUserCountRequestManager
{
    private readonly IRepository<IncreaseUserCountRequest, Guid> _increaseUserCountRequestRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _subscriptionRepository;
    protected BillManager BillManager => LazyServiceProvider.GetRequiredService<BillManager>();
    protected IncreaseUserCountRequestBillPlanFactory IncreaseUserCountRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<IncreaseUserCountRequestBillPlanFactory>();
    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    public IncreaseUserCountRequestManager(IRepository<IncreaseUserCountRequest, Guid> increaseUserCountRequestRepository, IRepository<TrackAccountSubscription, Guid> subscriptionRepository)
    {
        _increaseUserCountRequestRepository = increaseUserCountRequestRepository;
        _subscriptionRepository = subscriptionRepository;
    }

    public async Task MarkAsCompleted(Guid requestId)
    {
        var request = await _increaseUserCountRequestRepository.GetAsync(requestId);

        var subscription = await _subscriptionRepository.GetAsync(request.TrackAccountSubscriptionId);

        subscription.IncreaseUserCount(request.UserCount);

        await _subscriptionRepository.UpdateAsync(subscription);
        await _increaseUserCountRequestRepository.UpdateAsync(request);

        request.MarkAsCompleted();
    }

    public async Task<IncreaseUserCountRequest> CreateAsync(Guid ownerId, Guid subscriptionId, int observersCount, Guid trackAccountId)
    {
        var hasPendingPaymentRequest = await _increaseUserCountRequestRepository.AnyAsync(request =>
            request.TrackAccountSubscriptionId == subscriptionId
            && request.IncreaseUserCountRequestStage == IncreaseUserCountRequestStage.Payment
            && request.Status != RequestStatus.Canceled
        );

        if (hasPendingPaymentRequest)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateUserCountRequest);
        }

        var subscription = await _subscriptionRepository.GetAsync(subscriptionId);
        if (subscription.State != TrackAccountSubscriptionState.Active)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InactiveSubscription);
        }

        var increaseUserCountRequest = new IncreaseUserCountRequest(GuidGenerator.Create(), ownerId, observersCount, subscriptionId, trackAccountId);

        var billId = await BillManager.CreateBillAsync(await GenerateBillPlan(increaseUserCountRequest),true);
        increaseUserCountRequest.SetBillId(billId);

        await _increaseUserCountRequestRepository.InsertAsync(increaseUserCountRequest, true); //TODO

        return increaseUserCountRequest;
    }

    public async Task<IncreaseUserCountRequest> CreateTempAsync(Guid ownerId, Guid subscriptionId, int observersCount, Guid trackAccountId)
    {
        var renewalRequest = new IncreaseUserCountRequest(GuidGenerator.Create(), ownerId, observersCount, subscriptionId, trackAccountId);

        return renewalRequest;
    }

    public async Task HandlePaymentCompletionAsync(Guid requestId)
    {
        await MarkAsCompleted(requestId);
    }

    public async Task<BillPlan> GenerateBillPlan(IncreaseUserCountRequest request)
    {
        var remainingMonths = await TrackAccountSubscriptionManager.GetRemainingMonths(request.TrackAccountSubscriptionId);

        return await IncreaseUserCountRequestBillPlanFactory.GenerateBillPlan(
            new IncreaseUserCountRequestBillPlanInput(
                request.Id,
                request.OwnerId,
                request.UserCount,
                remainingMonths
            )
        );
    }
}