using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests.AddVehiclesRequests;

public class AddVehiclesRequestBillPlanFactory : DomainService, IBillPlanFactory<AddVehiclesRequestBillPlanInput>
{
    private BillingHelperService BillingHelperService => LazyServiceProvider.LazyGetRequiredService<BillingHelperService>();

    public async Task<BillPlan> GenerateBillPlan(AddVehiclesRequestBillPlanInput input)
    {
        var billLines = await GenerateBillLineItem(
            input.DevicesCount,
            input.TrackVehiclesCount,
            input.RemainingMonths,
            input.SubscriptionPlanDefinition
        );

        var data = GenerateSpecificationData(input);
        return new BillPlan(input.OwnerId, billLines, data);
    }

    private async Task<List<BillLineItem>> GenerateBillLineItem(
        int devicesCount,
        int trackVehiclesCount,
        int remainingMonths,
        SubscriptionPlanDefinition subscriptionPlanDefinition
        )
    {
        List<BillLineItem> billLineItems = [];

        billLineItems.Add(
            await BillingHelperService.GetLineItemAsync(subscriptionPlanDefinition.Key, trackVehiclesCount, remainingMonths));

        billLineItems.Add(
            await BillingHelperService.GetLineItemAsync(PricingItemKeys.Device, devicesCount, remainingMonths));
        billLineItems.Add(await BillingHelperService.GetLineItemAsync(PricingItemKeys.DeviceInstallation, trackVehiclesCount,
            remainingMonths));
        return billLineItems;
    }

    private Dictionary<string, string> GenerateSpecificationData(AddVehiclesRequestBillPlanInput input)
    {
        var data = new Dictionary<string, string>
        {
            [DiscountDataKeys.RequestType] = RequestType.AddVehiclesRequest.ToString(),
            [DiscountDataKeys.RequestId] = input.RequestId.ToString(),
        };

        if (!string.IsNullOrEmpty(input.PromoCode))
        {
            data[DiscountDataKeys.PromoCode] = input.PromoCode;
        }

        return data;
    }
}