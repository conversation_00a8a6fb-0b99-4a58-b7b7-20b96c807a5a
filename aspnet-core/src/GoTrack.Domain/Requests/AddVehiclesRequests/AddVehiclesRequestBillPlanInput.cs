using System;
using System.Collections.Generic;
using GoTrack;
using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;
using Volo.Abp;

namespace GoTrack.Requests.AddVehiclesRequests;

public class AddVehiclesRequestBillPlanInput : BillPlanInput
{
    public Guid OwnerId { get; private set; }
    public int DevicesCount { get; private set; }
    public int TrackVehiclesCount { get; private set; }
    public int RemainingMonths { get; private set; }
    public SubscriptionPlanDefinition SubscriptionPlanDefinition { get; private set; }
    public string? PromoCode { get; private set; }

    public AddVehiclesRequestBillPlanInput(
        Guid requestId,
        Guid ownerId,
        int devicesCount,
        int trackVehiclesCount,
        int remainingMonths,
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        string? promoCode = null)
        : base(requestId)
    {
        OwnerId = ownerId;
        RemainingMonths = remainingMonths;
        SubscriptionPlanDefinition = subscriptionPlanDefinition;
        DevicesCount = devicesCount;
        TrackVehiclesCount = trackVehiclesCount;
        PromoCode = promoCode;
        Validate();
    }

    protected sealed override void Validate()
    {
        base.Validate();

        if (OwnerId == Guid.Empty)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.OwnerIdRequired);
        }

        if (RemainingMonths <= 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RemainingMonthsInvalid);
        }

        if (DevicesCount < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountNegative);
        }

        if (TrackVehiclesCount <= 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.TrackVehiclesCountNegative);
        }

        if (DevicesCount > TrackVehiclesCount)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountExceedsVehiclesCount);
        }
    }
}