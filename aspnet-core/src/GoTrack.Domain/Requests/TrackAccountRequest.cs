using GoTrack.TrackAccounts;
using System;

namespace GoTrack.Requests;

public abstract class TrackAccountRequest : Request, IHaveTrackAccount
{
    public Guid TrackAccountId { get; private set; }

    protected TrackAccountRequest() : base() { }

    public TrackAccountRequest(Guid id, Guid ownerId, RequestType type, RequestStatus status, Guid trackAccountId)
        : base(id, ownerId, type, status)
    {
        TrackAccountId = trackAccountId;
    }
}
