using System;
using GoTrack.Payments.Bills;
using GoTrack.SubscriptionPlans;
using Volo.Abp;

namespace GoTrack.Requests.RenewTrackAccountSubscriptions;

public class RenewSubscriptionRequestBillPlanInput : BillPlanInput
{

    public Guid OwnerId { get; private set; }
    public SubscriptionPlanDefinition SubscriptionPlanDefinition { get; private set; }
    public int SubscriptionDurationInMonths { get; private set; }
    public int DevicesCount { get; private set; }
    public int CurrentVehicleCount { get; private set; }
    public int NumberOfAddedCars { get; private set; }
    public Guid? SmsBundleId { get; private set; }
    public string? PromoCode { get; private set; }

    public RenewSubscriptionRequestBillPlanInput(
        Guid requestId,
        Guid ownerId,
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        int subscriptionDurationInMonths,
        int devicesCount,
        int currentVehicleCount,
        int numberOfAddedCars,
        Guid? smsBundleId,
        string? promoCode = null)
        : base(requestId)
    {
        OwnerId = ownerId;
        SubscriptionPlanDefinition = subscriptionPlanDefinition;
        SubscriptionDurationInMonths = subscriptionDurationInMonths;
        CurrentVehicleCount = currentVehicleCount;
        NumberOfAddedCars = numberOfAddedCars;
        SmsBundleId = smsBundleId;
        DevicesCount = devicesCount;
        PromoCode = promoCode;
        Validate();
    }

    protected sealed override void Validate()
    {
        base.Validate();

        if (OwnerId == Guid.Empty)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.OwnerIdRequired);
        }

        if (SubscriptionDurationInMonths is < 1 or > 12)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionDuration);
        }
        
        if (DevicesCount < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountNegative);
        }

        if (NumberOfAddedCars < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.VehicleCountNegative);
        }

        if (DevicesCount > NumberOfAddedCars)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountExceedsVehiclesCount);
        }
    }
}