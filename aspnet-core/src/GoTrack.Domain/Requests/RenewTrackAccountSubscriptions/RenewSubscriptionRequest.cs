using System;
using System.Collections.Generic;
using GoTrack.Payments.Bills;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp;

namespace GoTrack.Requests.RenewTrackAccountSubscriptions;

public class RenewSubscriptionRequest : TrackAccountRequest, IBillableRequest, IPromoCodeRequest
{
    public string SubscriptionPlanKey { get; private set; }
    public int UserCount { get; private set; }
    public Guid? SmsBundleId { get; private set; }
    public List<SubscriptionVehicleInfo> NewTrackVehicles { get; private set; } = [];
    public List<Guid> RemoveTrackVehicles { get; private set; } = [];
    public TrackerInstallationLocation? TrackerInstallationLocation { get; private set; }
    public int SubscriptionDurationInMonths { get; private set; }
    public RenewSubscriptionRequestStage RenewSubscriptionRequestStage { get; private set; }
    public List<Guid> RemoveUsers { get; private set; } = [];
    public  Guid TrackAccountSubscriptionId { get; private set; }
    public Guid? BillId { get; set; }
    public string? PromoCode { get; private set; }
    public Guid? CreatedTrackAccountSubscriptionId { get; private set; }

    #region navigation
    public Bill? Bill { get; set; }
    public SmsBundle SmsBundle { get; set; }
    public  TrackAccountSubscription TrackAccountSubscription { get; private set; }
    #endregion

    private RenewSubscriptionRequest() : base()
    {
    }

    internal RenewSubscriptionRequest(
        Guid id,
        Guid ownerId,
        Guid trackAccountId,
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        int userCount,
        Guid trackAccountSubscriptionId,
        string? promoCode,
        Guid? smsBundleId = null,
        List<SubscriptionVehicleInfo>? newTrackVehicles = null,
        List<Guid>? removeTrackVehicles = null,
        List<Guid>? removeUsers = null,
        TrackerInstallationLocation? trackerInstallationLocation = null,
        int subscriptionDurationInMonths = 0)
        : base(
            id,
            ownerId,
            RequestType.RenewSubscription,
            RequestStatus.Pending,
            trackAccountId
        )
    {
        SubscriptionPlanKey = subscriptionPlanDefinition.Key;
        UserCount = userCount;
        TrackAccountSubscriptionId = trackAccountSubscriptionId;
        SmsBundleId = smsBundleId;
        NewTrackVehicles = newTrackVehicles ?? [];
        SubscriptionVehicleInfo.CheckTrackVehiclesForLicensePlateDuplicates(NewTrackVehicles);
        RemoveTrackVehicles = removeTrackVehicles ?? [];
        TrackerInstallationLocation = trackerInstallationLocation;
        SubscriptionDurationInMonths = Check.Range(subscriptionDurationInMonths, nameof(SubscriptionDurationInMonths), 1, 12);
        RenewSubscriptionRequestStage = RenewSubscriptionRequestStage.Review;
        RemoveUsers = removeUsers ?? [];
        PromoCode = promoCode;
    }

    internal void SetStageAsPayment()
    {
        if (this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.Review)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        RenewSubscriptionRequestStage = RenewSubscriptionRequestStage.Payment;
    }

    internal void SetStageAsPaymentReview()
    {
        if (this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.Payment)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);

        RenewSubscriptionRequestStage = RenewSubscriptionRequestStage.PaymentReview;
    }

    internal void SetStageAsDeviceInstallation()
    {
        if (TrackerInstallationLocation is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.TrackerInstallationLocationRequired);
        }

        if (this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.PaymentReview)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        RenewSubscriptionRequestStage = RenewSubscriptionRequestStage.DeviceInstallation;
    }

    internal void SetStageAsFinish()
    {
        if (this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.Review &&
            this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.DeviceInstallation &&
            (this.RenewSubscriptionRequestStage != RenewSubscriptionRequestStage.PaymentReview ||
             this.NewTrackVehicles.Count != 0))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        RenewSubscriptionRequestStage = RenewSubscriptionRequestStage.Finished;
    }

    public void SetBillId(Guid billId)
    {
        if (BillId is not null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillAlreadyAssigned);
        }

        BillId = billId;
    }
    
    internal void SetCreatedTrackAccountSubscription(Guid createdTrackAccountSubscriptionId)
    {
        if (CreatedTrackAccountSubscriptionId is not null)
            throw new BusinessException(GoTrackDomainErrorCodes.ValueIsAlreadySet);

        CreatedTrackAccountSubscriptionId = createdTrackAccountSubscriptionId;
    }
}