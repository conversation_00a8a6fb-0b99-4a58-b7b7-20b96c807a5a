using System;
using GoTrack.Payments.Bills;
using GoTrack.SmsBundles;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp;

namespace GoTrack.Requests.SmsBundleRenewalRequests;

public class SmsBundleRenewalRequest : TrackAccountRequest, IBillableRequest
{
    public Guid TrackAccountSubscriptionId { get; private set; }
    public SmsBundleRenewalStage SmsBundleRenewalStage { get; private set; }
    public Guid SmsBundleId { get; private set; }
    public Guid? BillId { get; set; }

    #region navigation
    public SmsBundle SmsBundle { get; private set; }
    public TrackAccountSubscription TrackAccountSubscription { get; private set; }
    public Bill? Bill { get; set; }
    #endregion


    private SmsBundleRenewalRequest()
    {
    }

    internal SmsBundleRenewalRequest(Guid id, Guid ownerId, Guid trackAccountSubscriptionId, Guid smsBundleId,
        Guid trackAccountId)
        : base(id, ownerId, RequestType.SmsBundleRenewalRequest, RequestStatus.Pending, trackAccountId)
    {
        TrackAccountSubscriptionId = trackAccountSubscriptionId;
        SmsBundleId = smsBundleId;
        SmsBundleRenewalStage = SmsBundleRenewalStage.Payment;
    }

    internal void MarkAsReadyForCompletion()
    {
        if (SmsBundleRenewalStage != SmsBundleRenewalStage.Payment)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidRenewalStage);
        }

        SmsBundleRenewalStage = SmsBundleRenewalStage.Finished;
        StartProcessing();
        FinishProcessing();
    }
    
    public void SetBillId(Guid billId)
    {
        if (BillId is not null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillAlreadyAssigned);
        }
        
        BillId = billId;
    }
}