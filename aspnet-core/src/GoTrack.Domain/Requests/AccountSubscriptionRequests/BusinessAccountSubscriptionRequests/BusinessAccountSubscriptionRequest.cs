using System;
using System.Collections.Generic;
using GoTrack.Addresses;
using GoTrack.Payments.Bills;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using Volo.Abp;

namespace GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public class BusinessAccountSubscriptionRequest : Request, IBillableRequest, IPromoCodeRequest
{
    public string CompanyName { get; private set; }
    public string AccountName { get; private set; }
    public Address CompanyAddress { get; private set; }
    public TrackerInstallationLocation TrackerInstallationLocation { get; private set; }
    public string SubscriptionPlanKey { get; private set; }
    public AccountSubscriptionRequestStage AccountSubscriptionRequestStage { get; private set; }
    public int UserCount { get; private set; }
    public List<SubscriptionVehicleInfo> TrackVehicles { get; private set; }
    public Guid? CreatedTrackAccountId { get; private set; }
    public Guid? SmsBundleId { get; private set; }
    public int SubscriptionDurationInMonths { get; private set; }
    public Guid? BillId { get; private set; }
    public string? PromoCode { get; private set; }

    #region navigation
    public Bill? Bill { get; set; }
    public SmsBundle? SmsBundle { get; set; }
    #endregion

    private BusinessAccountSubscriptionRequest() : base()
    {
    }

    internal BusinessAccountSubscriptionRequest(
        Guid id,
        Guid ownerId,
        string companyName,
        Address companyAddress,
        string accountName,
        TrackerInstallationLocation trackerInstallationLocation,
        List<SubscriptionVehicleInfo> trackVehicles,
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        int userCount,
        int subscriptionDurationInMonths,
        string? promoCode,
        Guid? smsBundleId = null
    ) : base(
        id,
        ownerId,
        RequestType.BusinessAccountSubscription,
        RequestStatus.Pending
    )
    {
        CompanyName = Check.NotNullOrEmpty(companyName, nameof(companyName));
        CompanyAddress = companyAddress;
        AccountName = Check.NotNullOrEmpty(accountName, nameof(accountName));
        TrackerInstallationLocation = trackerInstallationLocation;
        TrackVehicles = SubscriptionVehicleInfo.CheckTrackVehiclesForLicensePlateDuplicates(trackVehicles);
        SubscriptionPlanKey = subscriptionPlanDefinition.Key;
        SetUserCount(userCount);
        AccountSubscriptionRequestStage = AccountSubscriptionRequestStage.Review;
        SmsBundleId = smsBundleId;
        SetSubscriptionDuration(subscriptionDurationInMonths, subscriptionPlanDefinition);
        PromoCode = promoCode;
    }


    private void SetSubscriptionDuration(int months, SubscriptionPlanDefinition subscriptionPlanDefinition)
    {
        if (subscriptionPlanDefinition.RelatedSubscriptionDurationMonths is not null &&
            subscriptionPlanDefinition.RelatedSubscriptionDurationMonths!.Contains(months) is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionDurationRelation);
        }

        if (months is < 1 or > 12)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionDuration);
        }

        SubscriptionDurationInMonths = months;
    }

    private void SetUserCount(int userCount)
    {
        Check.Range(userCount, nameof(UserCount), 0);
        UserCount = userCount;
    }

    internal void SetStageAsFinish()
    {
        if (this.AccountSubscriptionRequestStage != AccountSubscriptionRequestStage.Review &&
            this.AccountSubscriptionRequestStage != AccountSubscriptionRequestStage.DeviceInstallation)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        AccountSubscriptionRequestStage = AccountSubscriptionRequestStage.Finished;
    }

    internal void SetStageAsPayment()
    {
        if (this.AccountSubscriptionRequestStage != AccountSubscriptionRequestStage.Review)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        AccountSubscriptionRequestStage = AccountSubscriptionRequestStage.Payment;
    }

    internal void SetStageAsPaymentReview()
    {
        if (this.AccountSubscriptionRequestStage != AccountSubscriptionRequestStage.Payment)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage); // TODO

        AccountSubscriptionRequestStage = AccountSubscriptionRequestStage.PaymentReview;
    }

    internal void SetStageAsDeviceInstallation()
    {
        if (this.AccountSubscriptionRequestStage != AccountSubscriptionRequestStage.PaymentReview)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);
        }

        AccountSubscriptionRequestStage = AccountSubscriptionRequestStage.DeviceInstallation;
    }

    internal void SetCreatedTrackAccount(Guid createdTrackAccountId)
    {
        if (CreatedTrackAccountId is not null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CreatedTrackAccountAlreadySet);
        }

        CreatedTrackAccountId = createdTrackAccountId;
    }

    public void SetBillId(Guid billId)
    {
        if (BillId is not null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillAlreadyAssigned);
        }

        BillId = billId;
    }
}