using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using GoTrack.SubscriptionPlans;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

public class PersonalAccountSubscriptionRequestBillPlanFactory : DomainService,
    IBillPlanFactory<PersonalAccountSubscriptionRequestBillPlanInput>
{
    private BillingHelperService BillingHelperService =>
        LazyServiceProvider.LazyGetRequiredService<BillingHelperService>();

    public async Task<BillPlan> GenerateBillPlan(PersonalAccountSubscriptionRequestBillPlanInput input)
    {
        var billLines = await GenerateBillLineItem(
            input.SubscriptionPlanDefinition,
            input.SubscriptionDurationInMonths,
            input.DevicesCount,
            input.TrackVehiclesCount,
            input.SmsBundleId
        );

        var data = GenerateSpecificationData(input);
        return new BillPlan(input.OwnerId, billLines, data);
    }

    private async Task<List<BillLineItem>> GenerateBillLineItem(
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        int subscriptionDurationInMonths,
        int devicesCount,
        int trackVehiclesCount,
        Guid? smsBundleId = null
    )
    {
        List<BillLineItem> billLineItems = [];

        billLineItems.Add(
            await BillingHelperService.GetLineItemAsync(subscriptionPlanDefinition.Key, trackVehiclesCount, subscriptionDurationInMonths));

        billLineItems.Add(await BillingHelperService.GetLineItemAsync(PricingItemKeys.Device, devicesCount,
            subscriptionDurationInMonths));
        billLineItems.Add(await BillingHelperService.GetLineItemAsync(PricingItemKeys.DeviceInstallation, trackVehiclesCount,
            subscriptionDurationInMonths));

        if (smsBundleId is not null)
        {
            billLineItems.Add(
                await BillingHelperService.GetSmsBundleLineItemAsync(smsBundleId.Value,
                    subscriptionDurationInMonths));
        }

        return billLineItems;
    }

    private Dictionary<string, string> GenerateSpecificationData(PersonalAccountSubscriptionRequestBillPlanInput input)
    {
        var data = new Dictionary<string, string>
        {
            [DiscountDataKeys.RequestDurationInMonth] = input.SubscriptionDurationInMonths.ToString(),
            [DiscountDataKeys.RequestType] = RequestType.PersonalAccountSubscription.ToString(),
            [DiscountDataKeys.RequestId] = input.RequestId.ToString()
        };

        if (!string.IsNullOrEmpty(input.PromoCode))
        {
            data[DiscountDataKeys.PromoCode] = input.PromoCode;
        }

        return data;
    }
}