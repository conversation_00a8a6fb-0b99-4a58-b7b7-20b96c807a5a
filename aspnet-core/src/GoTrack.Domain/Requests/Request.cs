using System;
using System.Collections.Generic;
using System.Linq;

using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;

namespace GoTrack.Requests;

public abstract class Request : FullAuditedAggregateRoot<Guid>
{
    public Guid OwnerId { get; private set; } 
    public RequestType Type { get; private set; }
    public RequestStatus Status { get; private set; }
    public string? RejectReason { get; private set; }
    public IReadOnlyCollection<RequestNote> Notes  => _notes.ToList().AsReadOnly() ?? throw new InvalidOperationException();
    private readonly ICollection<RequestNote> _notes;
    
    // navigations
    public IdentityUser Owner { get; private set; }
    
    protected Request()
    { 
        _notes = new List<RequestNote>();
    }

    protected Request(Guid id, Guid ownerId, RequestType type, RequestStatus status) : base(id)
    {
        OwnerId = ownerId;
        Type = type;
        Status = status;
        _notes = new List<RequestNote>();
    }
    
    public void AddNote(RequestNote requestNote)
    {
        _notes.Add(requestNote);
    }
    
    internal void MarkAsCanceled()
    {
        if (Status != RequestStatus.Pending)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidRequestStatusOnlyPendingCanCanceled);

        Status = RequestStatus.Canceled;
    }

    internal void MarkAsRejected(string rejectReason)
    {
        if (Status != RequestStatus.Pending)
            throw new BusinessException(GoTrackDomainErrorCodes.RequestCanOnlyBeRejectedIfPending);

        Status = RequestStatus.Rejected;
        RejectReason = rejectReason;
    }

    internal void StartProcessing()
    {
        if (Status != RequestStatus.Pending)
            throw new BusinessException(GoTrackDomainErrorCodes.RequestCanOnlyBeStartedIfPending);

        Status = RequestStatus.Processing;
    }

    internal void FinishProcessing()
    {
        if (Status != RequestStatus.Processing)
            throw new BusinessException(GoTrackDomainErrorCodes.RequestCanOnlyBeFinishedIfProcessing);

        Status = RequestStatus.Processed;
    }

}