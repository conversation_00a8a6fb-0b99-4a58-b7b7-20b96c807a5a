using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;
using Volo.Abp.Uow;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions.Workers;

public class NotifySubscriptionWorker : AsyncPeriodicBackgroundWorkerBase
{
    public NotifySubscriptionWorker(AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
    {
        Timer.Period = 12 * 60 * 60 * 1000;
    }

    [UnitOfWork]
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        Logger.LogInformation("Running NotifySubscriptionWorker...");

        var subscriptionManager = workerContext
            .ServiceProvider
            .GetRequiredService<TrackAccountSubscriptionManager>();

        await subscriptionManager.NotifyExpiringSubscriptionsAsync();
    }
}