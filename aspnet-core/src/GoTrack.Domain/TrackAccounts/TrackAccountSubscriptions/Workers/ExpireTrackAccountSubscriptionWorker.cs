using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;
using Volo.Abp.Uow;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions.Workers;

public class ExpireTrackAccountSubscriptionWorker : AsyncPeriodicBackgroundWorkerBase
{
    public ExpireTrackAccountSubscriptionWorker(AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
    {
        Timer.Period = 5 * 60 * 1000;
    }
    
    [UnitOfWork]
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {   
        Logger.LogInformation("Running ExpireTrackAccountSubscriptionWorker...");

        var subscriptionManager = workerContext
            .ServiceProvider
            .GetRequiredService<TrackAccountSubscriptionManager>();

        await subscriptionManager.ExpireSubscriptionsAsync();
    }
}
