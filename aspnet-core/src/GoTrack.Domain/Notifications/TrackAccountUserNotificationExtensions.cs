using Notify.Notifications.UserNotifications;
using System;
using Volo.Abp.Data;

namespace GoTrack.Notifications;

public static class TrackAccountUserNotificationExtensions
{
    public static void SetUserNotificationTrackAccountId(this UserNotification userNotification, Guid trackAccountId)
    {
        userNotification.SetProperty(NotifyProviderFCMConstsExtensions.UserNotificationTrackAccoundIdPropertyName, trackAccountId.ToString());
    }

    public static Guid? GetUserNotificationTrackAccountId(this UserNotification userNotification)
    {
        var trackAccountId = userNotification.GetProperty(NotifyProviderFCMConstsExtensions.UserNotificationTrackAccoundIdPropertyName);
        if (trackAccountId is null)
            return null;

        if (trackAccountId is not string trackAccountIdString)
            throw new NotImplementedException();

        return new Guid(trackAccountIdString);
    }
}
