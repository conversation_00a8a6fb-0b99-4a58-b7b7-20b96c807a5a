using Notify;
using Notify.Provider.FCM;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Notifications.NotificationFactories.TestFCMNotifications;

public class TestFCMNotificationFactory : NotificationFactory<TestFCMNotificationDataModel, CreateFCMNotificationEto>, ITransientDependency
{
    public override Task<CreateFCMNotificationEto> CreateAsync(TestFCMNotificationDataModel model, IEnumerable<Guid> userIds)
    {
        return Task.FromResult(new CreateFCMNotificationEto(model.Title, model.Body, userIds));
    }
}
