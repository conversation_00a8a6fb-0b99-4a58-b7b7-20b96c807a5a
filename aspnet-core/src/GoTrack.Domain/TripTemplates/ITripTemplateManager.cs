using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.TripTemplates;

public interface ITripTemplateManager : IDomainService
{
    Task AddVehicleGroupToTripTemplateAsync(Guid tripTemplateId, Guid vehicleGroupId);
    Task AddVehicleToTripTemplateAsync(Guid tripTemplateId, Guid vehicleId);
    Task<TripTemplate> CreateAsync(string name, Guid trackAccountId, List<Guid> routeIds);
}