using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Values;

namespace GoTrack.TripTemplates;

public class TripTemplateRoute : ValueObject
{
    public Guid TripTemplateId { get; private set; }
    public Guid RouteId { get; private set; }

    private TripTemplateRoute() { }
    internal TripTemplateRoute(Guid tripTemplateId, Guid routeId)
    {
        TripTemplateId = tripTemplateId;
        RouteId = routeId;
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return TripTemplateId;
        yield return RouteId;
    }
}
