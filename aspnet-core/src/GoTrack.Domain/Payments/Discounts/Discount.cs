using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Timing;

namespace GoTrack.Payments.Discounts;

public class Discount : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; private set; }
    public TargetType TargetType { get; private set; }
    public decimal Value { get; private set; }
    public bool IsPercentage { get; private set; }
    public DateTime StartDate { get; private set; }
    public List<string>? PricingItemKeys { get; private set; }
    public DateTime? EndDate { get; private set; }

    public List<DiscountCriteria> DiscountCriteriaList { get; private set; } = [];

    private Discount()
    {
    }

    internal Discount(
        Guid id,
        string name,
        TargetType targetType,
        decimal value,
        bool isPercentage,
        DateTime startDate,
        List<DiscountCriteria> discountCriteriaList,
        List<string>? pricingItemKeys = null,
        DateTime? endDate = null
    ) : base(id)
    {
        Name = name;
        TargetType = targetType;
        PricingItemKeys = pricingItemKeys;
        SetValue(isPercentage, value);
        StartDate = startDate;
        EndDate = endDate;
        DiscountCriteriaList = discountCriteriaList;
    }

    private void SetValue(bool isPercentage, decimal value)
    {
        if (isPercentage && value is <= 0 or > 1)
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountPercentageValueOutOfRange);

        if(isPercentage is false && value <= 0)
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountFixedValueMustBePositive);

        Value = value;
        IsPercentage = isPercentage;
    }



    public bool IsActive()
    {
        if (EndDate is null)
            return DateTime.UtcNow >= StartDate;

        return DateTime.UtcNow >= StartDate && DateTime.UtcNow < EndDate;
    }

    public decimal CalculateDiscountAmount(decimal subTotal) 
    {
        if (!IsPercentage)
            return subTotal - Value < 0 ? 0 : Value;

        return subTotal * Value;
    }

    public static Expression<Func<Discount, bool>> GetIsActiveExpression(IClock clock)
    {
        return discount => discount.EndDate == null
            ? clock.Now >= discount.StartDate 
            : clock.Now >= discount.StartDate && clock.Now < discount.EndDate;
    }

    public void SetEndDate(DateTime endDate)
    {
        if (StartDate >= endDate)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange);
        }
        EndDate = endDate;
    }
    
    public void UpdateDate(DateTime startDate,DateTime endDate)
    {
        if (startDate >= endDate)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange);
        }
        StartDate = startDate;
        EndDate = endDate;
    }
}