using System.Linq;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PricingItems;
using Volo.Abp;

namespace GoTrack.Payments.Discounts.Specifications;

public class PricingItemDiscountSpecification : IDiscountSpecification
{
    private readonly StaticPricingItemDefinitionStore _staticPricingItemDefinitionStore;

    public PricingItemDiscountSpecification(StaticPricingItemDefinitionStore staticPricingItemDefinitionStore)
    {
        _staticPricingItemDefinitionStore = staticPricingItemDefinitionStore;
    }

    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.PricingItemKey;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        var requiredPricingItemKey = criteria.SpecificationValue;

        return billPlan.BillLineItems.Any(x => x.PricingItemKey == requiredPricingItemKey);
    }

    public void CheckIfValidAsync(string data)
    {
        var pricingItemDefinition = _staticPricingItemDefinitionStore.GetOrNull(data);

        if (pricingItemDefinition == null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationPricingItemInvalid);
        }
    }
}