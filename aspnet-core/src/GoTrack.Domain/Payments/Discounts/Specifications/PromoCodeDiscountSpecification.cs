using System;
using System.Collections.Generic;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PromoCodes;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Threading;

namespace GoTrack.Payments.Discounts.Specifications;

public class PromoCodeDiscountSpecification : IDiscountSpecification
{
    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.PromoCode;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        if (billPlan.DiscountSpecificationData.GetValueOrDefault(DiscountDataKeys.PromoCode) is null)
        {
            return false;
        }

        var promoCode = billPlan.DiscountSpecificationData[DiscountDataKeys.PromoCode];
        var requiredPromoCode = criteria.SpecificationValue;

        return promoCode.ToUpper() == requiredPromoCode.ToUpper();
    }

    public void CheckIfValidAsync(string data)
    {
        if (string.IsNullOrWhiteSpace(data))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeRequired);
        }
    }
}
