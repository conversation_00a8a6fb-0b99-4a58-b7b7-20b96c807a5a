using System.Collections.Generic;
using GoTrack.Payments.Bills;
using Volo.Abp;

namespace GoTrack.Payments.Discounts.Specifications;

public class DurationDiscountSpecification : IDiscountSpecification
{
    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.Duration;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        if (billPlan.DiscountSpecificationData.GetValueOrDefault(DiscountDataKeys.RequestDurationInMonth) is null)
        {
            return false;
        }

        var requestDurationInMonth = int.Parse(billPlan.DiscountSpecificationData[DiscountDataKeys.RequestDurationInMonth]);
        var requiredMonths = int.Parse(criteria.SpecificationValue);

        return requestDurationInMonth == requiredMonths;
    }

    public void CheckIfValidAsync(string data)
    {
        if (!int.TryParse(data, out var months) || months <= 0 || months > 12)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationDurationInvalid);
        }
    }
}