using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Values;

namespace GoTrack.Payments.PricingItems;

public class Price : ValueObject
{
    public decimal Amount { get; private set; }
    public DateTime EffectiveDate { get; private set; }

    private Price()
    {
    }

    public Price(decimal amount, DateTime effectiveDate)
    {
        Amount = amount;
        EffectiveDate = effectiveDate;
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Amount;
        yield return EffectiveDate;
    }
}