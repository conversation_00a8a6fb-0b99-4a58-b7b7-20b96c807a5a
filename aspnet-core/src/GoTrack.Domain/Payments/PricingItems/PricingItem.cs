using System;
using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Payments.PricingItems;

public class PricingItem : FullAuditedAggregateRoot<Guid>
{
    public string Key { get; private set; }
    public decimal CurrentPrice { get; private set; }

    private readonly List<Price> _priceHistory = [];
    public IReadOnlyList<Price> PriceHistory => _priceHistory.AsReadOnly();
    
    private PricingItem()
    {
    }

    public PricingItem(string key, decimal price)
    {
        Check.NotNullOrWhiteSpace(key, nameof(key));
        Key = key;
        SetCurrentPrice(price);
    }
    
    private void SetCurrentPrice(decimal price)
    {
        if (price < 0) throw new BusinessException(GoTrackDomainErrorCodes.PriceCannotBeNegative);
        CurrentPrice = price;
    }

    public void UpdatePrice(decimal newPrice)
    {
        if (newPrice < 0) throw new BusinessException(GoTrackDomainErrorCodes.PriceCannotBeNegative);
        _priceHistory.Add(new Price(newPrice, DateTime.Now));
        CurrentPrice = newPrice;
    }
}