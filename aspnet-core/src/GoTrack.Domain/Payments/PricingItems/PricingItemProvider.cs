using GoTrack.Localization;
using GoTrack.SubscriptionPlans;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Localization;

namespace GoTrack.Payments.PricingItems;

public class PricingItemProvider : ITransientDependency
{
    public void Define(IPricingItemContext context)
    {
        context.AddPricingItem(PricingItemKeys.Device, PricingType.OneTime, L(PricingItemKeys.Device));
        context.AddPricingItem(PricingItemKeys.DeviceInstallation, PricingType.OneTime, L(PricingItemKeys.DeviceInstallation));
        context.AddPricingItem(PricingItemKeys.AdditionalUsers, PricingType.Monthly, L(PricingItemKeys.AdditionalUsers));
        context.AddPricingItem(PricingItemKeys.SmsBundle, PricingType.Monthly, L(PricingItemKeys.SmsBundle), false);
        context.AddPricingItem(SubscriptionPlanKeys.Silver, PricingType.Monthly,L(SubscriptionPlanKeys.Silver));
        context.AddPricingItem(SubscriptionPlanKeys.Gold, PricingType.Monthly,L(SubscriptionPlanKeys.Gold));
        context.AddPricingItem(SubscriptionPlanKeys.Platinum, PricingType.Monthly,L(SubscriptionPlanKeys.Platinum));
        context.AddPricingItem(SubscriptionPlanKeys.PlatinumTrial, PricingType.Monthly,L(SubscriptionPlanKeys.PlatinumTrial));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<GoTrackResource>(name);
    }
}