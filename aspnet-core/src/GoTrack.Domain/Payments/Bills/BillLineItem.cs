using System;
using GoTrack.Payments.PricingItems;
using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Domain.Values;

namespace GoTrack.Payments.Bills;

public class BillLineItem : ValueObject
{
    public string PricingItemKey { get; private set; }
    public decimal UnitPrice { get; private set; }
    public int Quantity { get; private set; }
    public PricingType PricingType { get; private set; }
    public int? RequestedMonths { get; private set; }
    public decimal BillableAmount { get; private set; }
    private BillLineItem()
    {
    }

    public BillLineItem(string pricingItemKey, decimal unitPrice, int quantity, PricingType pricingType, int? requestedMonths = null)
    {
        PricingItemKey = pricingItemKey;
        UnitPrice = unitPrice;
        Quantity = quantity;
        PricingType = pricingType;
        RequestedMonths = requestedMonths;

        if (pricingType is PricingType.Monthly && requestedMonths is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RequestedMonthsCannotBeNull);
        }
        
        BillableAmount = Math.Round(CalculateTotal());
    }

    private decimal CalculateTotal()
    {
        if (PricingType is PricingType.Monthly && RequestedMonths is not null)
        {
            return UnitPrice * Quantity * RequestedMonths.Value;
        }
        return UnitPrice * Quantity;
    }
    
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return PricingItemKey;
        yield return UnitPrice;
        yield return Quantity;
    }
}