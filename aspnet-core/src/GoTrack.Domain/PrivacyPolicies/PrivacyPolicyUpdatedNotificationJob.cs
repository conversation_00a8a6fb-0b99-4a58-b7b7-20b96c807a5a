using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Localization;
using GoTrack.TrackAccounts;
using Microsoft.Extensions.Localization;
using Notify.Provider.FCM;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace GoTrack.PrivacyPolicies;

public class PrivacyPolicyUpdatedNotificationJob : AsyncBackgroundJob<PrivacyPolicyUpdatedNotificationJobArgs>, ITransientDependency
{
    private readonly IIdentityUserRepository _identityUserRepository;
    private readonly IStringLocalizer<GoTrackResource> _localizer;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IDataFilter _dataFilter;
    
    public PrivacyPolicyUpdatedNotificationJob(IIdentityUserRepository identityUserRepository, IStringLocalizer<GoTrackResource> localizer, IDistributedEventBus distributedEventBus, IDataFilter dataFilter)
    {
        _identityUserRepository = identityUserRepository;
        _localizer = localizer;
        _distributedEventBus = distributedEventBus;
        _dataFilter = dataFilter;
    }

    [UnitOfWork]
    public override async Task ExecuteAsync(PrivacyPolicyUpdatedNotificationJobArgs args)
    {
        using var _ = _dataFilter.Disable<IHostTenantUserFilter>();
        using var __ = _dataFilter.Disable<ICustomerUserFilter>();
        using var ___ = _dataFilter.Disable<IHaveTrackAccount>();
        
        var totalUserCount = await _identityUserRepository.GetCountAsync();
        
        var skipCount = 0;
        var pageSize = 500;
        
        while (skipCount < totalUserCount)
        {
            var users = await _identityUserRepository.GetPagedListAsync(skipCount, pageSize, "");
            
            var message = _localizer[
                "GoTrack:PrivacyPolicyUpdatedMessage"
            ];
            
            var title = _localizer["GoTrack:PrivacyPolicyTitle"];
            var fcmEto = new CreateFCMNotificationEto(title, message, users.Select(x => x.Id));
            await _distributedEventBus.PublishAsync(fcmEto);

            skipCount += pageSize;
        }
    }
}
