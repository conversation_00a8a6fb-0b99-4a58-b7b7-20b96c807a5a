using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.Devices;
using GoTrack.GeoZones;
using GoTrack.Identity;
using GoTrack.Msisdns;
using GoTrack.Observations;
using GoTrack.Payments.Bills;
using GoTrack.PolyLines;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.SeedWork;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using GoTrack.Users;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using GoTrack.Vehicles.LicensePlates;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace GoTrack.Data;

public class ExampleDataSeeder : IDataSeedContributor,ITransientDependency
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExampleDataSeeder> _logger;
    
    private readonly IRepository<Device, Guid> _deviceRepository;
    private readonly IRepository<BusinessAccountSubscriptionRequest, Guid> _businessAccountSubscriptionRequestRepository;
    private readonly IRepository<PersonalAccountSubscriptionRequest, Guid> _personalAccountSubscriptionRequestRepository;
    private readonly IIdentityUserRepository _identityUserRepository;
    private readonly IRepository<Observation, Guid> _observationRepository;
    private readonly IRepository<ObservationVehicle, Guid> _observationVehicleRepository;
    private readonly IRepository<ObservationVehicleGroup, Guid> _observationVehicleGroupRepository;
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly IRepository<VehicleGroup, Guid> _vehicleGroupRepository;
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<GeoZone, Guid> _geoZoneRepository;
    private readonly IRepository<VehicleGroupVehicle, Guid> _vehicleGroupVehicleRepository;
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;
    
    private readonly IGuidGenerator _guidGenerator;
    private readonly IMsisdnManager _msisdnManager;
    private readonly IBusinessAccountSubscriptionRequestManager _businessAccountSubscriptionRequestManager;
    private readonly IPersonalAccountSubscriptionRequestManager _personalAccountSubscriptionRequestManager;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IdentityUserProfileManager _identityUserProfileManager;
    private readonly IDeviceManager _deviceManager;
    private readonly IUserTrackAccountAssociationManager _userTrackAccountAssociationManager;
    private readonly RequestManager _requestManager;
    private readonly GeoZoneManager _geoZoneManager;
    private readonly IDataFilter _dataFilter;

    public ExampleDataSeeder(
        IConfiguration configuration,
        ILogger<ExampleDataSeeder> logger,
        IRepository<Device, Guid> deviceRepository,
        IRepository<BusinessAccountSubscriptionRequest, Guid> businessAccountSubscriptionRequestRepository,
        IRepository<PersonalAccountSubscriptionRequest, Guid> personalAccountSubscriptionRequestRepository,
        IIdentityUserRepository identityUserRepository,
        IRepository<Observation, Guid> observationRepository,
        IRepository<ObservationVehicle, Guid> observationVehicleRepository,
        IRepository<ObservationVehicleGroup, Guid> observationVehicleGroupRepository,
        IRepository<Vehicle, Guid> vehicleRepository,
        IRepository<VehicleGroup, Guid> vehicleGroupRepository,
        IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IRepository<GeoZone, Guid> geoZoneRepository,
        IRepository<VehicleGroupVehicle, Guid> vehicleGroupVehicleRepository,
        IRepository<SmsBundle, Guid> smsBundleRepository,
        IGuidGenerator guidGenerator,
        IMsisdnManager msisdnManager,
        IBusinessAccountSubscriptionRequestManager businessAccountSubscriptionRequestManager,
        IPersonalAccountSubscriptionRequestManager personalAccountSubscriptionRequestManager,
        IUnitOfWorkManager unitOfWorkManager,
        IdentityUserProfileManager identityUserProfileManager,
        IDeviceManager deviceManager,
        IUserTrackAccountAssociationManager userTrackAccountAssociationManager,
        RequestManager requestManager,
        GeoZoneManager geoZoneManager, IDataFilter dataFilter)
    {
        _configuration = configuration;
        _logger = logger;
        _deviceRepository = deviceRepository;
        _businessAccountSubscriptionRequestRepository = businessAccountSubscriptionRequestRepository;
        _personalAccountSubscriptionRequestRepository = personalAccountSubscriptionRequestRepository;
        _identityUserRepository = identityUserRepository;
        _observationRepository = observationRepository;
        _observationVehicleRepository = observationVehicleRepository;
        _observationVehicleGroupRepository = observationVehicleGroupRepository;
        _vehicleRepository = vehicleRepository;
        _vehicleGroupRepository = vehicleGroupRepository;
        _trackAccountRepository = trackAccountRepository;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _geoZoneRepository = geoZoneRepository;
        _vehicleGroupVehicleRepository = vehicleGroupVehicleRepository;
        _smsBundleRepository = smsBundleRepository;
        _guidGenerator = guidGenerator;
        _msisdnManager = msisdnManager;
        _businessAccountSubscriptionRequestManager = businessAccountSubscriptionRequestManager;
        _personalAccountSubscriptionRequestManager = personalAccountSubscriptionRequestManager;
        _unitOfWorkManager = unitOfWorkManager;
        _identityUserProfileManager = identityUserProfileManager;
        _deviceManager = deviceManager;
        _userTrackAccountAssociationManager = userTrackAccountAssociationManager;
        _requestManager = requestManager;
        _geoZoneManager = geoZoneManager;
        _dataFilter = dataFilter;
    }
    
    public async Task SeedAsync(DataSeedContext context)
    {
        if (context.TenantId is not null)
        {
            return;
        }
        
        if (!ShouldSeedExampleData())
        {
            _logger.LogInformation("Example data seeding is disabled for current environment");
            return;
        }

        using var _ = _dataFilter.Disable<IHaveTrackAccount>();

        _logger.LogInformation("Starting example data seeding...");

        await SeedExampleCustomers();
        await SeedDevices();
        await SeedExampleBusinessAccountSubscriptionRequest();
        await SeedExamplePersonalAccountSubscriptionRequest();
        // await SeedExampleBusinessAccountSubscriptionRequestNonCompleted();
        // await SeedExamplePersonalAccountSubscriptionRequestNonComplete();
        await SeedVehicleGroup();
        await SeedUserTrackAccountAssociation();
        await SeedObserverAssociations();
        await SeedZoneAsync();

        _logger.LogInformation("Example data seeding completed successfully.");
    }

    private bool ShouldSeedExampleData()
    {
        return _configuration.GetValue<bool>("DataSeeding:EnableExampleData");
    }

    private async Task SeedExampleCustomers()
    {
        _logger.LogInformation("Seeding example customers...");

        if (await _identityUserRepository.FindAsync(SampleData.Customer1Id) is not null)
        {
            _logger.LogInformation("Example customers already exist, skipping seeding.");
            return;
        }

        var msisdn1 = _msisdnManager.Create("+************");

        var customerUserWithCompleteProfile = new IdentityUser(
            SampleData.Customer1Id,
            msisdn1.ToString(),
            "<EMAIL>",
            null
        );

        customerUserWithCompleteProfile.SetPhoneNumber(msisdn1.ToString(), true);
        customerUserWithCompleteProfile.SetSubClass(UserSubClass.Customer);
        customerUserWithCompleteProfile.Name = "عبد الجليل";
        customerUserWithCompleteProfile.Surname = "سورس";
        customerUserWithCompleteProfile.AddClaim(
            _guidGenerator,
            new Claim(UsersConstants.ProfileRequiresUpdateClaimName, "False")
        );

        await _identityUserRepository.InsertAsync(customerUserWithCompleteProfile, true);

        await _identityUserProfileManager.CreateAsync(
            customerUserWithCompleteProfile,
            new Address("1st", "Mezzeh", "damascus", "damascus", "syria")
        );

        var msisdn2 = _msisdnManager.Create("+963 00 5550200");

        var customerUserWithIncompleteProfile = new IdentityUser(
            SampleData.Customer2Id,
            msisdn2.ToString(),
            "<EMAIL>",
            null
        );

        customerUserWithIncompleteProfile.SetPhoneNumber(msisdn2.ToString(), true);
        customerUserWithIncompleteProfile.SetSubClass(UserSubClass.Customer);
        customerUserWithIncompleteProfile.AddClaim(
            _guidGenerator,
            new Claim(UsersConstants.ProfileRequiresUpdateClaimName, "True")
        );

        await _identityUserRepository.InsertAsync(customerUserWithIncompleteProfile, true);

        _logger.LogInformation("Example customers seeded successfully.");
    }

    private async Task SeedDevices()
    {
        _logger.LogInformation("Seeding example devices...");

        if (await _deviceRepository.AnyAsync())
        {
            _logger.LogInformation("Devices already exist, skipping seeding.");
            return;
        }

        var modelDictionary = new Dictionary<string, string>
        {
            { "en", "model" },
            { "ar", "model" }
        };
        var brandDictionary = new Dictionary<string, string>
        {
            { "en", "brand" },
            { "ar", "brand" }
        };

        await _deviceManager.CreateAsync(
            new LocalizedString(modelDictionary),
            new LocalizedString(brandDictionary),
            "***************",
            "sim",
            Protocol.p1
        );

        await _deviceManager.CreateAsync(
            new LocalizedString(modelDictionary),
            new LocalizedString(brandDictionary),
            "***************",
            "sim2",
            Protocol.p1
        );

        await _deviceManager.CreateAsync(
            new LocalizedString(modelDictionary),
            new LocalizedString(brandDictionary),
            "864371061100543",
            "sim3",
            Protocol.p1
        );

        await _deviceManager.CreateAsync(
            new LocalizedString(modelDictionary),
            new LocalizedString(brandDictionary),
            "***************",
            "sim3",
            Protocol.p1
        );

        var device = await _deviceManager.CreateAsync(
            new LocalizedString(modelDictionary),
            new LocalizedString(brandDictionary),
            "***************",
            "sim3",
            Protocol.p1
        );

        device.SetOwnerId(SampleData.Customer1Id);
        await _deviceRepository.UpdateAsync(device, true);
        await _deviceManager.SetDeviceActiveAsync(device.Id);
        await _deviceManager.SetDeviceDeactiveAsync(device.Id);

        _logger.LogInformation("Example devices seeded successfully.");
    }

    private async Task SeedExampleBusinessAccountSubscriptionRequest()
    {
        _logger.LogInformation("Seeding example business account subscription request...");

        using var uow = _unitOfWorkManager.Begin(
            requiresNew: false, isTransactional: false
        );

        if (await _businessAccountSubscriptionRequestRepository.AnyAsync())
        {
            _logger.LogInformation("Business account subscription requests already exist, skipping seeding.");
            return;
        }

        var smsBundle = await _smsBundleRepository.FirstOrDefaultAsync();

        var subscriptionVehicleInfo = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.Damascus,
                "929000"
            ),
            Color.Green,
            0.1,
            true
        );

        var subscriptionVehicleInfo2 = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.AlHasakah,
                "357268"
            ),
            Color.Green,
            0.2,
            true
        );

        var requestId = await _businessAccountSubscriptionRequestManager.CreateAsync(
            SampleData.Customer1Id,
            "CS",
            new Address("1st", "Mezzeh", "damascus", "damascus", "syria"),
            "CS",
            TrackerInstallationLocation.OnSite,
            [subscriptionVehicleInfo, subscriptionVehicleInfo2],
            SubscriptionPlanKeys.Platinum,
            3,
            4,
            null,
            smsBundle?.Id
        );
        await uow.SaveChangesAsync();

        var request =
            await _businessAccountSubscriptionRequestRepository.GetAsync(requestId);
        await _businessAccountSubscriptionRequestManager.ApplyDiscountAsync(request.Id, 0.5m, "Start Processing");
        await uow.SaveChangesAsync();

        // Move to Payment Review
        await _requestManager.ProcessPaymentAsync(request,PaymentMethod.Cash,"Pay cash from seeder");
        await uow.SaveChangesAsync();

        // Install devices
        var device = await _deviceRepository.GetAsync(x => x.Imei == "***************");
        var device2 = await _deviceRepository.GetAsync(x => x.Imei == "***************");

        var deviceRequests = new List<DeviceInstallationRequest>()
        {
            new()
            {
                DeviceId = device.Id,
                LicensePlateSubClass = subscriptionVehicleInfo.LicensePlate.SubClass,
                Serial = subscriptionVehicleInfo.LicensePlate.Serial
            },
            new()
            {
                DeviceId = device2.Id,
                LicensePlateSubClass = subscriptionVehicleInfo2.LicensePlate.SubClass,
                Serial = subscriptionVehicleInfo2.LicensePlate.Serial
            }
        };

        await _businessAccountSubscriptionRequestManager.InstallDevicesAsync(
            request.Id,
            deviceRequests
        );
        await uow.SaveChangesAsync();

        // Finish processing
        await _businessAccountSubscriptionRequestManager.FinishProcessingAsync(request.Id, "Finish Processing");
        await _unitOfWorkManager.Current?.SaveChangesAsync()!;

        await uow.CompleteAsync();

        _logger.LogInformation("Example business account subscription request seeded successfully.");
    }

    private async Task SeedExampleBusinessAccountSubscriptionRequestNonCompleted()
    {
        _logger.LogInformation("Seeding example non-completed business account subscription request...");

        using var uow = _unitOfWorkManager.Begin(
            requiresNew: false, isTransactional: false
        );

        if (await _businessAccountSubscriptionRequestRepository.AnyAsync(x => x.AccountSubscriptionRequestStage == AccountSubscriptionRequestStage.PaymentReview))
        {
            _logger.LogInformation("Non-completed business account subscription requests already exist, skipping seeding.");
            return;
        }

        var smsBundle = await _smsBundleRepository.FirstOrDefaultAsync();

        var subscriptionVehicleInfo = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.Damascus,
                "929000"
            ),
            Color.Green,
            0.1,
            true
        );

        var subscriptionVehicleInfo2 = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.AlHasakah,
                "357268"
            ),
            Color.Green,
            0.2,
            true
        );

        var subscriptionVehicleInfo3 = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.Hama,
                "054560"
            ),
            Color.Bisque,
            0.6,
            false
        );

        var requestId = await _businessAccountSubscriptionRequestManager.CreateAsync(
            SampleData.Customer1Id,
            "Kadmous",
            new Address("1st", "Mezzeh", "damascus", "damascus", "syria"),
            "Kadmous",
            TrackerInstallationLocation.OnSite,
            [subscriptionVehicleInfo, subscriptionVehicleInfo2, subscriptionVehicleInfo3],
            SubscriptionPlanKeys.Platinum,
            3,
            4,
            null,
            smsBundle?.Id
        );
        await uow.SaveChangesAsync();

        var request =
            await _businessAccountSubscriptionRequestRepository.GetAsync(requestId);
        await _businessAccountSubscriptionRequestManager.ApplyDiscountAsync(request.Id, 0.5m, "Start Processing");
        await uow.SaveChangesAsync();

        // Move to Payment Review
        await _requestManager.ProcessPaymentAsync(request,PaymentMethod.Cash,"Pay cash from seeder");
        await uow.SaveChangesAsync();

        await uow.CompleteAsync();

        _logger.LogInformation("Example non-completed business account subscription request seeded successfully.");
    }

    private async Task SeedExamplePersonalAccountSubscriptionRequest()
    {
        _logger.LogInformation("Seeding example personal account subscription request...");

        using var uow = _unitOfWorkManager.Begin(
            requiresNew: false, isTransactional: false
        );

        if (await _personalAccountSubscriptionRequestRepository.AnyAsync())
        {
            _logger.LogInformation("Personal account subscription requests already exist, skipping seeding.");
            return;
        }

        var smsBundle = await _smsBundleRepository.FirstOrDefaultAsync();

        var subscriptionVehicleInfo = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.Damascus,
                "402135"
            ),
            Color.Blue,
            0.1,
            true
        );

        var requestId = await _personalAccountSubscriptionRequestManager.CreateAsync(
            SampleData.Customer1Id,
            "Personal Account",
            TrackerInstallationLocation.OnSite,
            [subscriptionVehicleInfo],
            SubscriptionPlanKeys.Platinum,
            3,
            12,
            new Address("1st", "Mezzeh", "damascus", "damascus", "syria"),
            null,
            smsBundle?.Id
        );
        await uow.SaveChangesAsync();

        var request =
            await _personalAccountSubscriptionRequestRepository.GetAsync(requestId);
        await _personalAccountSubscriptionRequestManager.ApplyDiscountAsync(request.Id, 0.5m, "Start Processing");
        await uow.SaveChangesAsync();

        await _requestManager.ProcessPaymentAsync(request,PaymentMethod.Cash,"Pay cash from seeder");
        await uow.SaveChangesAsync();

        // Install devices
        var device = await _deviceRepository.GetAsync(x => x.Imei == "***************");

        var deviceRequests = new List<DeviceInstallationRequest>()
        {
            new()
            {
                DeviceId = device.Id,
                LicensePlateSubClass = subscriptionVehicleInfo.LicensePlate.SubClass,
                Serial = subscriptionVehicleInfo.LicensePlate.Serial
            }
        };

        await _personalAccountSubscriptionRequestManager.InstallDevicesAsync(
            request.Id,
            deviceRequests
        );
        await uow.SaveChangesAsync();

        await _personalAccountSubscriptionRequestManager.FinishProcessingAsync(request.Id, "Finish Processing");
        await uow.SaveChangesAsync();

        await uow.CompleteAsync();

        _logger.LogInformation("Example personal account subscription request seeded successfully.");
    }

    private async Task SeedExamplePersonalAccountSubscriptionRequestNonComplete()
    {
        _logger.LogInformation("Seeding example non-complete personal account subscription request...");

        using var uow = _unitOfWorkManager.Begin(
            requiresNew: false, isTransactional: false
        );

        if (await _personalAccountSubscriptionRequestRepository.AnyAsync(x => x.AccountSubscriptionRequestStage == AccountSubscriptionRequestStage.PaymentReview))
        {
            _logger.LogInformation("Non-complete personal account subscription requests already exist, skipping seeding.");
            return;
        }

        var smsBundle = await _smsBundleRepository.FirstOrDefaultAsync();

        var subscriptionVehicleInfo = new SubscriptionVehicleInfo(
            new SubscriptionVehicleLicensePlate(
                VehicleLicensePlateSubClass.Damascus,
                "432135"
            ),
            Color.Blue,
            0.1,
            true
        );

        var requestId = await _personalAccountSubscriptionRequestManager.CreateAsync(
            SampleData.Customer1Id,
            "Personal Account",
            TrackerInstallationLocation.OnSite,
            [subscriptionVehicleInfo],
            SubscriptionPlanKeys.Platinum,
            3,
            12,
            new Address("1st", "Mezzeh", "damascus", "damascus", "syria"),
            null,
            smsBundle?.Id
        );
        await uow.SaveChangesAsync();

        var request =
            await _personalAccountSubscriptionRequestRepository.GetAsync(requestId);
        await _personalAccountSubscriptionRequestManager.ApplyDiscountAsync(request.Id, 0.5m, "Start Processing");
        await uow.SaveChangesAsync();

        await _requestManager.ProcessPaymentAsync(request,PaymentMethod.Cash,"Pay cash from seeder");
        await uow.SaveChangesAsync();

        await uow.CompleteAsync();

        _logger.LogInformation("Example non-complete personal account subscription request seeded successfully.");
    }

    private async Task SeedVehicleGroup()
    {
        _logger.LogInformation("Seeding example vehicle group...");

        if (await _vehicleGroupRepository.AnyAsync())
        {
            _logger.LogInformation("Vehicle groups already exist, skipping seeding.");
            return;
        }

        var trackAccount = await _trackAccountRepository.FindAsync(x =>
            x.AccountType == TrackAccountTypes.Business
            && x.UserTrackAccountAssociations.Any(y =>
                y.UserId == SampleData.Customer1Id
            )
        );
        
        if (trackAccount is null)
        {
            _logger.LogWarning("Track account not found for vehicle group seeding.");
            return;
        }

        var vehicles = await _vehicleRepository.GetListAsync(x => x.TrackAccountId == trackAccount.Id);
        if (vehicles.Count < 2)
        {
            _logger.LogWarning("Not enough vehicles found for vehicle group seeding.");
            return;
        }

        var vehicleGroup = new VehicleGroup(_guidGenerator.Create(), "FirstGroup", trackAccount.Id);

        var newVehicleGroupVehicle = new VehicleGroupVehicle(_guidGenerator.Create(), vehicles[0].Id, vehicleGroup.Id);
        var newVehicleGroupVehicle2 = new VehicleGroupVehicle(_guidGenerator.Create(), vehicles[1].Id, vehicleGroup.Id);

        await _vehicleGroupVehicleRepository.InsertManyAsync([newVehicleGroupVehicle, newVehicleGroupVehicle2]);

        await _vehicleGroupRepository.InsertAsync(vehicleGroup, true);

        _logger.LogInformation("Example vehicle group seeded successfully.");
    }

    private async Task SeedUserTrackAccountAssociation()
    {
        _logger.LogInformation("Seeding example user track account association...");

        if (await _userTrackAccountAssociationRepository.CountAsync() > 1)
        {
            _logger.LogInformation("User track account associations already exist, skipping seeding.");
            return;
        }

        var trackAccount = await _trackAccountRepository.FindAsync(x =>
            x.UserTrackAccountAssociations.Any(y =>
                y.UserId == SampleData.Customer1Id
            )
        );
        if (trackAccount is null)
        {
            _logger.LogWarning("Track account not found for user track account association seeding.");
            return;
        }

        var msisdn = _msisdnManager.Create("**************");
        var userTrackAccountAssociation = await _userTrackAccountAssociationManager.CreateObserverAsync(
            trackAccount.Id,
            msisdn,
            "Eyas"
        );

        await _userTrackAccountAssociationRepository.InsertAsync(userTrackAccountAssociation, true);

        _logger.LogInformation("Example user track account association seeded successfully.");
    }

    private async Task SeedObserverAssociations()
    {
        _logger.LogInformation("Seeding example observer associations...");

        if (await _observationRepository.AnyAsync())
        {
            _logger.LogInformation("Observer associations already exist, skipping seeding.");
            return;
        }

        var trackAccount = await _trackAccountRepository.FirstOrDefaultAsync(x =>
            x.UserTrackAccountAssociations.Any(y =>
                y.UserId == SampleData.Customer1Id
            )
        );
        if (trackAccount is null)
        {
            _logger.LogWarning("Track account not found for observer associations seeding.");
            return;
        }

        var msisdn = _msisdnManager.Create("**************");

        var userTrackAccountAssociation =
            await _userTrackAccountAssociationRepository.FindAsync(x =>
                x.TrackAccountId == trackAccount.Id && x.PhoneNumber == msisdn);
        if (userTrackAccountAssociation is null)
        {
            _logger.LogWarning("User track account association not found for observer associations seeding.");
            return;
        }

        var vehicles = await _vehicleRepository.GetListAsync(x => x.TrackAccountId == trackAccount.Id);
        if (vehicles.Count < 2)
        {
            _logger.LogWarning("Not enough vehicles found for observer associations seeding.");
            return;
        }

        var observerVehicle = new ObservationVehicle(
            _guidGenerator.Create(),
            userTrackAccountAssociation.Id,
            vehicles[0].Id
        );

        await _observationVehicleRepository.InsertAsync(observerVehicle, true);

        var vehicleGroup =
            await _vehicleGroupRepository.FindAsync(x => x.TrackAccountId == trackAccount.Id && x.Name == "FirstGroup");
        if (vehicleGroup is null)
        {
            _logger.LogWarning("Vehicle group not found for observer associations seeding.");
            return;
        }

        var observerVehicleGroup = new ObservationVehicleGroup(
            _guidGenerator.Create(),
            userTrackAccountAssociation.Id,
            vehicleGroup.Id
        );

        await _observationVehicleGroupRepository.InsertAsync(observerVehicleGroup, true);

        _logger.LogInformation("Example observer associations seeded successfully.");
    }

    private async Task SeedZoneAsync()
    {
        _logger.LogInformation("Seeding example zone...");

        if (await _geoZoneRepository.AnyAsync())
        {
            _logger.LogInformation("Zones already exist, skipping seeding.");
            return;
        }

        var trackAccount = await _trackAccountRepository.FirstOrDefaultAsync(x =>
            x.UserTrackAccountAssociations.Any(y =>
                y.UserId == SampleData.Customer1Id
            )
        );
        if (trackAccount is null)
        {
            _logger.LogWarning("Track account not found for zone seeding.");
            return;
        }

        var newPolyline = new PolyLine("e{rkEiv}{E~vKczRqxSgvT`q@ncW");

        await _geoZoneManager.CreateAsync("FirstZone", newPolyline, trackAccount.Id);

        _logger.LogInformation("Example zone seeded successfully.");
    }
}
