using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ZoneAlertGeoZones;
using GoTrack.GeoZones;
using GoTrack.TrackAccounts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.DisassembleTrackingDevices;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.TrackableEntities;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.AlertDefinitions;

public class AlertDefinitionManager : DomainService, IAlertDefinitionManager
{
    private readonly IRepository<EnteringZoneAlertDefinition, Guid> _enteringZoneAlertDefinitionRepository;
    private readonly IRepository<ExceedingSpeedAlertDefinition, Guid> _exceedingSpeedAlertDefinitionRepository;
    private readonly IRepository<ExitingZoneAlertDefinition, Guid> _exitingZoneAlertDefinitionRepository;
    private readonly IRepository<GeoZone, Guid> _geoZoneRepository;
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;
    private readonly IRepository<JobTimeAlertDefinition, Guid> _jobTimeAlertDefinitionRepository;
    private readonly IRepository<AlertDefinition, Guid> _alertRepository;
    private readonly IRepository<AlertDefinitionAssociation, Guid> _alertDefinitionAssociationRepository;
    private readonly IRepository<TrackableEntityAssociation, Guid> _trackableEntityAssociationRepository;
    private readonly IAlertTriggerManagerResolver _alertTriggerManagerResolver;

    public AlertDefinitionManager(
        IRepository<EnteringZoneAlertDefinition, Guid> enteringZoneAlertDefinitionsRepository,
        IRepository<ExceedingSpeedAlertDefinition, Guid> exceedingSpeedAlertDefinitionRepository,
        IRepository<ExitingZoneAlertDefinition, Guid> exitingZoneAlertDefinitionRepository,
        IRepository<GeoZone, Guid> geoZoneRepository,
        IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<JobTimeAlertDefinition, Guid> jobTimeAlertDefinitionRepository,
        IRepository<AlertDefinition, Guid> alertRepository,
        IRepository<DisassembleTrackingDeviceAlertDefinition, Guid> disassembleTrackingDeviceAlertDefinitionRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<TrackableEntityAssociation, Guid> trackableEntityAssociationRepository,
        IAlertTriggerManagerResolver alertTriggerManagerResolver)
    {
        _enteringZoneAlertDefinitionRepository = enteringZoneAlertDefinitionsRepository;
        _exceedingSpeedAlertDefinitionRepository = exceedingSpeedAlertDefinitionRepository;
        _exitingZoneAlertDefinitionRepository = exitingZoneAlertDefinitionRepository;
        _geoZoneRepository = geoZoneRepository;
        _trackAccountRepository = trackAccountRepository;
        _jobTimeAlertDefinitionRepository = jobTimeAlertDefinitionRepository;
        _alertRepository = alertRepository;
        _alertDefinitionAssociationRepository = alertDefinitionAssociationRepository;
        _trackableEntityAssociationRepository = trackableEntityAssociationRepository;
        _alertTriggerManagerResolver = alertTriggerManagerResolver;
    }


    protected async Task CreateAsync(AlertDefinition alertDefinition, List<AlertDefinitionNotificationMethod> notificationMethods, Guid trackAccountId, List<Guid> vehicleIds, List<Guid> vehicleGroupIds)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);
        
        var alertDefinitionAssociations = new List<AlertDefinitionAssociation>();
        var trackableEntityAssociations = new List<TrackableEntityAssociation>();
        
        foreach (var vehicleId in vehicleIds) 
        {
            var newVehicleTrackableEntityAssociation = new VehicleTrackableEntityAssociation(GuidGenerator.Create(), vehicleId);

            trackableEntityAssociations.Add(newVehicleTrackableEntityAssociation);
            
            alertDefinitionAssociations.Add(
                new AlertDefinitionAssociation(
                    GuidGenerator.Create(),
                    alertDefinition.Id,
                    newVehicleTrackableEntityAssociation.Id
                )
            );
        }

        foreach (var vehicleGroupId in vehicleGroupIds)
        {
            var newVehicleGroupTrackableEntityAssociation = new VehicleGroupTrackableEntityAssociation(GuidGenerator.Create(), vehicleGroupId);

            trackableEntityAssociations.Add(newVehicleGroupTrackableEntityAssociation);
            
            alertDefinitionAssociations.Add(
                new AlertDefinitionAssociation(
                    GuidGenerator.Create(),
                    alertDefinition.Id,
                    newVehicleGroupTrackableEntityAssociation.Id
                )
            );
        }

        await _trackableEntityAssociationRepository.InsertManyAsync(trackableEntityAssociations, autoSave: true);

        await _alertDefinitionAssociationRepository.InsertManyAsync(alertDefinitionAssociations, autoSave: true);
        
        await _alertRepository.InsertAsync(alertDefinition,true);

        var alertTriggerManager = _alertTriggerManagerResolver.GetAlertTriggerManager(alertDefinition.Type);

        await alertTriggerManager.CreateAsync(alertDefinition.Id);
    }

    public async Task<EnteringZoneAlertDefinition> CreateEnteringZoneAlertDefinitionAsync(
        List<Guid> geoZoneIds,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        var allGeoZoneIds = geoZoneIds.ToHashSet();
        var existingGeoZones = await _geoZoneRepository.GetListAsync(x => allGeoZoneIds.Contains(x.Id));
        if (existingGeoZones.Count != allGeoZoneIds.Count)
            throw new EntityNotFoundException(typeof(GeoZone));

        var alertDefinitionId = GuidGenerator.Create();

        var newZoneAlertGeoZones = new List<ZoneAlertGeoZone>();
        foreach (var geoZoneId in allGeoZoneIds)
        {
            newZoneAlertGeoZones.Add(
                new ZoneAlertGeoZone(GuidGenerator.Create(), alertDefinitionId, geoZoneId)
            );
        }

        var newEnteringZoneAlertDefinition = new EnteringZoneAlertDefinition(
            alertDefinitionId,
            newZoneAlertGeoZones,
            trackAccountId,
            notificationMethods
        );

        return await _enteringZoneAlertDefinitionRepository.InsertAsync(newEnteringZoneAlertDefinition);
    }

    public async Task<ExceedingSpeedAlertDefinition> CreateExceedingSpeedAlertDefinitionAsync(
        decimal maxSpeed,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        //await SpeedAlertIsExistsAsync(maxSpeed, notificationMethod);

        var newExceedingSpeedAlertDefinition = new ExceedingSpeedAlertDefinition(
            GuidGenerator.Create(),
            maxSpeed,
            trackAccountId,
            notificationMethods
        );

        return await _exceedingSpeedAlertDefinitionRepository.InsertAsync(newExceedingSpeedAlertDefinition);
    }

    public async Task<ExitingZoneAlertDefinition> CreateExitingZoneAlertDefinitionAsync(
        List<Guid> geoZoneIds,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        var allGeoZoneIds = geoZoneIds.ToHashSet();
        var existingGeoZones = await _geoZoneRepository.GetListAsync(x => allGeoZoneIds.Contains(x.Id));
        if (existingGeoZones.Count != allGeoZoneIds.Count)
            throw new EntityNotFoundException(typeof(GeoZone));

        var alertDefinitionId = GuidGenerator.Create();

        var newZoneAlertGeoZones = new List<ZoneAlertGeoZone>();
        foreach (var geoZoneId in allGeoZoneIds)
        {
            newZoneAlertGeoZones.Add(
                new ZoneAlertGeoZone(GuidGenerator.Create(), alertDefinitionId, geoZoneId)
            );
        }

        var newExitingZoneAlertDefinition = new ExitingZoneAlertDefinition(
            alertDefinitionId,
            newZoneAlertGeoZones,
            trackAccountId,
            notificationMethods
        );

        return await _exitingZoneAlertDefinitionRepository.InsertAsync(newExitingZoneAlertDefinition);
    }

    public async Task<JobTimeAlertDefinition> CreateJobTimeAlertDefinitionAsync(
        string name,
        TimeOnly startTime,
        TimeOnly endTime,
        List<DayOfWeek> daysOfWeek,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        //await JobTimeAlertIsExistsAsync(startTime, endTime, daysOfWeek, notificationMethods);

        var newJobTimeAlertDefinition = new JobTimeAlertDefinition(
            GuidGenerator.Create(),
            name,
            startTime,
            endTime,
            daysOfWeek,
            trackAccountId,
            notificationMethods
        );

        return await _jobTimeAlertDefinitionRepository.InsertAsync(newJobTimeAlertDefinition);
    }

    public async Task EnabledAlertAsync(Guid alertDefinitionId)
    {
        var alertDefinition = await _alertRepository.GetAsync(alertDefinitionId);
        alertDefinition.SetIsEnabled(true);

        var alertTriggerManager = _alertTriggerManagerResolver.GetAlertTriggerManager(alertDefinition.Type);

        await alertTriggerManager.CreateAsync(alertDefinition.Id);

        await _alertRepository.UpdateAsync(alertDefinition);
    }

    public async Task DisabledAlertAsync(Guid alertDefinitionId)
    {
        var alertDefinition = await _alertRepository.GetAsync(alertDefinitionId);
        alertDefinition.SetIsEnabled(false);

        var alertTriggerManager = _alertTriggerManagerResolver.GetAlertTriggerManager(alertDefinition.Type);

        await alertTriggerManager.DeleteAsync(alertDefinition.Id);

        await _alertRepository.UpdateAsync(alertDefinition);
    }

    public async Task RemoveAsync(Guid alertDefinitionId)
    {
        var alertDefinition = await _alertRepository.GetAsync(alertDefinitionId);
        var alertTriggerManager = _alertTriggerManagerResolver.GetAlertTriggerManager(alertDefinition.Type);

        await alertTriggerManager.DeleteAsync(alertDefinition.Id);
        
        await _alertRepository.DeleteAsync(alertDefinition);
    }

    //private async Task SpeedAlertIsExistsAsync(decimal maxSpeed, AlertDefinitionNotificationMethod notificationMethod)
    //{
    //    var isExists = await _exceedingSpeedAlertDefinitionRepository.AnyAsync(alert =>
    //        alert.MaxSpeed == maxSpeed &&
    //        alert.NotificationMethod == notificationMethod
    //    );

    //    if (isExists)
    //        throw new BusinessException(GoTrackDomainErrorCodes.AlertOnVehicleOrVehicleGroupAlreadyExists);
    //}

    //private async Task JobTimeAlertIsExistsAsync(
    //    TimeOnly startTime,
    //    TimeOnly endTime,
    //    List<DayOfWeek> daysOfWeek,
    //    AlertDefinitionNotificationMethod notificationMethod)
    //{
    //    var isExists = await _jobTimeAlertDefinitionRepository.AnyAsync(alert =>
    //        alert.StartTime == startTime &&
    //        alert.EndTime == endTime &&
    //        alert.DaysOfWeek == daysOfWeek &&
    //        alert.NotificationMethod == notificationMethod
    //    );

    //    if (isExists)
    //        throw new BusinessException(GoTrackDomainErrorCodes.AlertOnVehicleOrVehicleGroupAlreadyExists);
    //}
    
    public async Task DeactivateAllAlertsOfTrackAccountAsync(Guid trackAccountId)
    {
        var query = await _alertRepository.GetQueryableAsync();
        query = query.Where(definition =>
            definition.TrackAccountId == trackAccountId
            && definition.IsEnabled == true);

        var alertDefinitions = await AsyncExecuter.ToListAsync(query);

        foreach (var alertDefinition in alertDefinitions)
        {
            await DisabledAlertAsync(alertDefinition.Id);
        }
    }
}