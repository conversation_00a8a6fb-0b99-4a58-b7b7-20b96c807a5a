using System;
using System.Collections.Generic;

namespace GoTrack.AlertDefinitions.JobTimeAlertDefinitions;

public class JobTimeAlertDefinition : AlertDefinition
{
    public string Name { get; private set; }
    public TimeOnly StartTime { get; private set; }
    public TimeOnly EndTime { get; private set; }
    
    public IReadOnlyCollection<DayOfWeek> DaysOfWeek => _daysOfWeek.AsReadOnly();
    private readonly List<DayOfWeek> _daysOfWeek = [];
    
    private JobTimeAlertDefinition() : base() { }

    internal JobTimeAlertDefinition(
        Guid id,
        string name,
        TimeOnly startTime,
        TimeOnly endTime,
        List<DayOfWeek> daysOfWeek,
        Guid trackAccountId,
        List<AlertDefinitionNotificationMethod> notificationMethods
        ) : base(id, trackAccountId, notificationMethods, AlertType.JobTime)
    {
        Name = name;
        StartTime = startTime;
        EndTime = endTime;
        _daysOfWeek = daysOfWeek;
    }
}
