using System;
using System.Collections.Generic;
using GoTrack.TrackAccounts;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.AlertDefinitions;

public abstract class AlertDefinition : FullAuditedAggregateRoot<Guid>, IHaveTrackAccount
{
    public AlertType Type { get; private set; }
    public Guid TrackAccountId { get; private set; }
    public bool IsEnabled { get; private set; }

    public IReadOnlyCollection<AlertDefinitionNotificationMethod> NotificationMethods => _notificationMethods.AsReadOnly();
    private readonly List<AlertDefinitionNotificationMethod> _notificationMethods = []; 

    protected AlertDefinition()
    {
    }

    protected AlertDefinition(
        Guid id,
        Guid trackAccountId,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        AlertType type
        ) : base(id)
    {
        Type = type;
        TrackAccountId = trackAccountId;
        _notificationMethods = notificationMethods;
        IsEnabled = true;
    }

    internal void SetIsEnabled(bool isEnabled)
    {
        IsEnabled = isEnabled;
    }
}