using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ZoneAlertGeoZones;
using System;
using System.Collections.Generic;

namespace GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;

public class ExitingZoneAlertDefinition : AlertDefinition
{
    #region Navigation
    public IReadOnlyCollection<ZoneAlertGeoZone> ZoneAlertGeoZones => _zoneAlertGeoZones.AsReadOnly();
    private readonly List<ZoneAlertGeoZone> _zoneAlertGeoZones = [];

    #endregion
    private ExitingZoneAlertDefinition() : base() { }

    internal ExitingZoneAlertDefinition(
        Guid id,
        List<ZoneAlertGeoZone> zoneAlertGeoZones,
        Guid trackAccountId,
        List<AlertDefinitionNotificationMethod> notificationMethods
        ) : base(id, trackAccountId, notificationMethods, AlertType.ExitingZone)
    {
        _zoneAlertGeoZones = zoneAlertGeoZones;
    }
}
