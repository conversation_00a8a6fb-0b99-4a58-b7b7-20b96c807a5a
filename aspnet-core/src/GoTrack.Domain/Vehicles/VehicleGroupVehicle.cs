using System;
using GoTrack.VehicleGroups;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Vehicles;

public class VehicleGroupVehicle : AuditedEntity<Guid>
{
    public Guid VehicleId { get; private set; }
    public Guid VehicleGroupId { get; private set; }

    #region Navigation
    
    public Vehicle Vehicle { get; private set; }
    public VehicleGroup VehicleGroup { get; private set; }
    
    #endregion
    
    private VehicleGroupVehicle()
    {
    }

    public VehicleGroupVehicle(Guid id, Guid vehicleId, Guid vehicleGroupId) : base(id)
    {
        VehicleId = vehicleId;
        VehicleGroupId = vehicleGroupId;
    }

    public override bool Equals(object? obj)
    {
        if (obj is not VehicleGroupVehicle other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        return VehicleId == other.VehicleId && VehicleGroupId == other.VehicleGroupId;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(VehicleId, VehicleGroupId);
    }
}