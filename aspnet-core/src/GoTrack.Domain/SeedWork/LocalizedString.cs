using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Domain.Values;
using Volo.Abp.Localization;

namespace GoTrack.SeedWork;

public class LocalizedString : ValueObject
{
    public Dictionary<string,string> LValue { get; protected set; }
    
    public LocalizedString(Dictionary<string,string> value)
    {
        LValue = value;
        VerifyValidLocale();
    }

    private void VerifyValidLocale()
    {
        foreach (var key in LValue.Keys)
        {
            if (!CultureHelper.IsValidCultureCode(key))
            {
                throw new BusinessException(GoTrackDomainErrorCodes.InvalidKey);
            }
        }
    }

    public string GetValue(string language)
    {
        return LValue[language];
    }
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return LValue;
    }
    
}