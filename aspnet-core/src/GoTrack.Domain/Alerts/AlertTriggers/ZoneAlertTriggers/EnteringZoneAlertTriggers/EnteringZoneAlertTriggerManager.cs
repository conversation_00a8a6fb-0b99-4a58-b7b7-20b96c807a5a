using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.GeoZones;
using GoTrack.PolyLines;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Warp10Abstraction.Models;

namespace GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers;

public class EnteringZoneAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<EnteringZoneAlertTrigger, Guid> _enteringZoneAlertTriggerRepository;
    private readonly IRepository<GeoZone, Guid> _geoZoneRepository;

    public EnteringZoneAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<EnteringZoneAlertTrigger, Guid> enteringZoneAlertTriggerRepository,
        IRepository<GeoZone, Guid> geoZoneRepository)
        : base(
            alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint,
            stringLocalizerFactory
        )
    {
        _enteringZoneAlertTriggerRepository = enteringZoneAlertTriggerRepository;
        _geoZoneRepository = geoZoneRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query = await _enteringZoneAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle, x => x.GeoZone);

        var enteringZoneAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));
        
        var message = Localizer[
            "GoTrack:EnteringZoneMessage",
            enteringZoneAlertTrigger.Vehicle.LicensePlate.Serial,
            enteringZoneAlertTrigger.GeoZone.Name,
            notificationDateTime.ToString("yyyy-MM-dd HH:mm:ss tt")
        ];

        return message;
    }

    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var enteringZoneAlertDefinition = alertDefinition as EnteringZoneAlertDefinition;

        return uniqueVehicles
            .SelectMany(vehicle => enteringZoneAlertDefinition!.ZoneAlertGeoZones
                .Select(zoneAlertGeoZone => new EnteringZoneAlertTrigger(
                    GuidGenerator.Create(),
                    [.. enteringZoneAlertDefinition.NotificationMethods],
                    enteringZoneAlertDefinition.TrackAccountId,
                    enteringZoneAlertDefinition.Id,
                    vehicle.Id,
                    zoneAlertGeoZone.GeoZoneId
                ))
            ).ToList<AlertTrigger>();
    }

    public override async Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var enteringZoneAlertTrigger = trigger as EnteringZoneAlertTrigger;

        var lastVehicleDeviceEventLog = (await VehicleDeviceEventLogRepository.WithDetailsAsync(x => x.Device))
                .OrderByDescending(e => e.CreationTime)
                .FirstOrDefault(log => log.VehicleId == enteringZoneAlertTrigger!.VehicleId && log.EventName == EventName.Installed);

        var enteringZoneDefinitionZone = await _geoZoneRepository.GetAsync(enteringZoneAlertTrigger!.GeoZoneId);
        var enteringZoneHhCode = await WarpLib.GetZoneHhCodeAsync(
            PolyLine.Decode(enteringZoneDefinitionZone.Polyline.Line).Select(coordinate =>
                    new WarpCoordinate(coordinate.Longitude, coordinate.Latitude)
                )
                .ToList()
        );

        return new ZoneInOutAlertCrud()
        {
            Id = enteringZoneAlertTrigger.Id,
            Code = AlertCode.ZoneIn,
            AffectiveFrom = Clock.Now,
            CrudType = crudType,
            Imei = lastVehicleDeviceEventLog?.Device.Imei,
            Polygon = enteringZoneHhCode
        };
    }
}
