using GoTrack.AlertDefinitions;
using System;
using GoTrack.TrackAccounts;
using Volo.Abp.Domain.Entities.Auditing;
using System.Collections.Generic;
using GoTrack.Vehicles;

namespace GoTrack.Alerts.AlertTriggers;

public abstract class AlertTrigger : FullAuditedAggregateRoot<Guid>, IHaveTrackAccount
{
    public AlertType Type { get; private set; }

    public IReadOnlyCollection<AlertDefinitionNotificationMethod> NotificationMethods => _notificationMethods.AsReadOnly();
    private readonly List<AlertDefinitionNotificationMethod> _notificationMethods = [];
    
    public Guid TrackAccountId { get; private set; }
    public Guid AlertDefinitionId { get; private set; }
    public Guid VehicleId { get; private set; }

    #region Navigation
    
    public Vehicle Vehicle { get; private set; }
    
    #endregion
    
    protected AlertTrigger() { }

    protected AlertTrigger(
        Guid id,
        AlertType type,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        Guid alertDefinitionId,
        Guid vehicleId
    ) : base(id)
    {
        Type = type;
        _notificationMethods = notificationMethods;
        TrackAccountId = trackAccountId;
        AlertDefinitionId = alertDefinitionId;
        VehicleId = vehicleId;
    }
}
