using System;
using System.Collections.Generic;
using GoTrack.AlertDefinitions;
using Volo.Abp;

namespace GoTrack.Alerts.AlertTriggers.DisassembleTrackingDevices;

public class DisassembleTrackingDeviceAlertTrigger : AlertTrigger
{
    public DisassembleTrackingDeviceAlertTrigger(
        Guid id,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        Guid alertDefinitionId,
        Guid vehicleId
    ) : base(
        id,
        AlertType.DisassembleTrackingDevice,
        notificationMethods,
        trackAccountId,
        alertDefinitionId,
        vehicleId
    )
    {
    }
}