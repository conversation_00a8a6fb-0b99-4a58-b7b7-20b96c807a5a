using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions;
using GoTrack.VehicleDeviceEventLogs;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using Volo.Abp.Domain.Repositories;
using System.Threading.Tasks;
using System.Linq;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.AlertDefinitions.DisassembleTrackingDevices;
using GoTrack.Vehicles;
using System.Collections.Generic;

namespace GoTrack.Alerts.AlertTriggers.DisassembleTrackingDevices;

public class DisassembleTrackingDeviceAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<DisassembleTrackingDeviceAlertTrigger, Guid> _disassembleTrackingDeviceAlertTriggerRepository;

    public DisassembleTrackingDeviceAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<DisassembleTrackingDeviceAlertTrigger, Guid> disassembleTrackingDeviceAlertTriggerRepository)
        : base(
            alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint,
            stringLocalizerFactory
        )
    {
        _disassembleTrackingDeviceAlertTriggerRepository = disassembleTrackingDeviceAlertTriggerRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query =
            await _disassembleTrackingDeviceAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle);

        var disassembleTrackingDeviceAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));

        var message = Localizer[
            "GoTrack:DisassembleTrackingDeviceMessage",
            disassembleTrackingDeviceAlertTrigger.Vehicle.LicensePlate.Serial,
            notificationDateTime.ToString("yyyy-MM-dd HH:mm:ss tt")
        ];

        return message;
    }

    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var disassembleTrackingDeviceAlertDefinition = alertDefinition as DisassembleTrackingDeviceAlertDefinition;

        return uniqueVehicles.Select(vehicle =>
            new DisassembleTrackingDeviceAlertTrigger(
                GuidGenerator.Create(),
                [.. disassembleTrackingDeviceAlertDefinition!.NotificationMethods],
                disassembleTrackingDeviceAlertDefinition.TrackAccountId,
                disassembleTrackingDeviceAlertDefinition.Id,
                vehicle.Id
            )
        ).ToList<AlertTrigger>();
    }

    public override Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var disassembleTrackingDeviceAlertTrigger = trigger as DisassembleTrackingDeviceAlertTrigger;

        return Task.FromResult<AlertCrud>(
            new DisassembleTrackingDeviceAlertCrud()
            {
                Id = disassembleTrackingDeviceAlertTrigger!.Id,
                Code = AlertCode.DisassembleTrackingDeviceAlertCrud,
                AffectiveFrom = Clock.Now,
                CrudType = crudType
            }
        );
    }
}
