using GoTrack.AlertDefinitions;
using System;
using System.Collections.Generic;

namespace GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers;

public class ExitingRouteAlertTrigger : AlertTrigger
{
    public Guid RouteId { get; private set; }

    private ExitingRouteAlertTrigger() { }

    public ExitingRouteAlertTrigger(
        Guid id,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        Guid alertDefinitionId,
        Guid vehicleId,
        Guid routeId
        ) : base(
            id,
            AlertType.ExitingRoute,
            notificationMethods,
            trackAccountId,
            alertDefinitionId,
            vehicleId
        )
    {
        RouteId = routeId;
    }
}
