using System;
using System.Threading.Tasks;
using GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions;

public interface ITrackAccountSubscriptionAppService : IApplicationService
{
    Task<TrackAccountSubscriptionDetailDto> GetAsync(Guid id);
    Task<TrackAccountSubscriptionDetailDto> GetCurrentSubscriptionAsync();
    Task<PagedResultDto<TrackAccountSubscriptionDto>> GetListAsync(PagedResultRequestDto requestDto);
}