using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Mobile.AlertDefinitions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.GeoZones;

public interface IGeoZoneAppService : IApplicationService
{
    public Task<PagedResultDto<GeoZoneDto>> GetListAsync(PagedResultRequestDto input);
    public Task<Guid> CreateAsync(GeoZoneCreateDto createDto);
    public Task<GeoZoneDetailsDto> GetAsync( Guid id);
}