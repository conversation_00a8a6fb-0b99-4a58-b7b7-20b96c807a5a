using System;
using System.Threading.Tasks;
using GoTrack.Mobile.Payments.DTOs;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.Payments;

public interface IPaymentAppService : IApplicationService
{
    Task GetCheckPaymentStatusAsync(Guid userFatoraPaymentId);
    Task<string> PayDev(CreatePaymentDto input);
    Task ReversalPaymentDevAsync(Guid paymentId);
    Task<string> PayMultipleRequestsAsync(CreateMultiplePaymentsDto input);
}
