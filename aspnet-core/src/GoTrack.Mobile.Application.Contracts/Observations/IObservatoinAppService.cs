using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using GoTrack.Mobile.Observations.DTOs;
using GoTrack.Mobile.UserTrackAccountAssociations.DTOs;

namespace GoTrack.Mobile.Observations;

public interface IObservatoinAppService : IApplicationService
{
    Task ActivateObserverAsync([Required] Guid userId);
    Task AddObservationVehicleAsync([Required] Guid userTrackAccountAssociationId, [Required] Guid vehicleId);
    Task AddObservationVehicleGroupAsync([Required] Guid userTrackAccountAssociationId, [Required] Guid vehicleGroupId);
    Task CreateObserverAsync(CreateObserverDto input);
    Task DeactivateObserverAsync([Required] Guid userId);
    Task DeleteObserverAsync([Required] string phoneNumber);
    Task<PagedResultDto<UserTrackAccountAssociationDto>> GetListAsync(PagedResultRequestDto input);
    Task<PagedResultDto<ObservationViewModelDto>> GetListVehicleAndVehicleGroupOfObseverAsync([Required] Guid userTrackAccountAssociationId, PagedResultRequestDto input);
    Task RemoveObservationVehicleAsync([Required] Guid userTrackAccountAssociationId, [Required] Guid vehicleId);
    Task RemoveObservationVehicleGroupAsync([Required] Guid userTrackAccountAssociationId, [Required] Guid vehicleGroupId);
    Task<UserTrackAccountAssociationDto> UpdateObserverAsync(UpdateObserverDto input);
}
