using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests;

namespace GoTrack.Mobile.Requests.AddVehiclesRequests.DTOs;

public class CreateAddVehiclesRequestsDto
{
    public List<SubscriptionVehicleInfoCreateDto> TrackVehicles { get; set; } = [];
    public bool HasValidDevice { get; set; }
    
    [StringLength(8, MinimumLength = 4)]
    public string? PromoCode { get; set; }
}
