using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Mobile.Monitoring.DTOs.DeviceHistory;
using GoTrack.Mobile.Monitoring.DTOs.LiveLocations;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.Monitoring;

public interface IMonitoringAppService : IApplicationService
{
    Task<List<LiveLocationDto>> PostLiveLocationAsync(LiveLocationInputDto liveLocationInputDto);
    Task<List<WarpGtsHistoryDto>> GetVehicleHistoryAsync(VehicleHistoryInputDto vehicleHistoryInputDto);
}
