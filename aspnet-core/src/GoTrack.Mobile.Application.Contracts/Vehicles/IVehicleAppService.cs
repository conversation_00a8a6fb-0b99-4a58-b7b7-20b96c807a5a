using GoTrack.Mobile.Vehicles.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.Vehicles;

public interface IVehicleAppService
{
    Task<PagedResultDto<VehicleDto>> GetListAsync(GetVehicleListRequestDto requestDto);
    Task AddAddToVehicleGroup(Guid id, Guid vehicleGroupId);
    Task RemoveRemoveFromVehicleGroup(Guid id, Guid vehicleGroupId);
    Task UpdateUpdateConsumptionRate(Guid id, double consumptionRate);
    Task<List<VehicleViewModelWithAuditingDto>> GetListVehicleViewModelWithAuditingAsync();
    Task<VehicleDto> GetAsync(Guid id);
}