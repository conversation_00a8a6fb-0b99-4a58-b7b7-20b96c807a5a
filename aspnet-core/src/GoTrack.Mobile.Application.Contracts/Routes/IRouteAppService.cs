using GoTrack.Mobile.Routes.DTOs;
using GoTrack.Mobile.StopPoints.DTOs;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.Routes;

public interface IRouteAppService : IApplicationService
{
    Task<RouteDto> AddStopPointToRouteAsync(Guid routeId, CreateStopPointDto createStopPointDto);
    Task<RouteDto> CreateAsync(CreateRouteDto createRouteDto);
    Task DeleteAsync(Guid id);
    Task<RouteViewModelDto> GetAsync(Guid id);
    Task<PagedResultDto<RouteDto>> GetListAsync(PagedResultRequestDto input);
    Task<PagedResultDto<StopPointDto>> GetStopPointsOnRouteAsync(Guid routeId, PagedResultRequestDto input);
    Task RemoveStopPointFromRouteAsync(Guid routeId, Guid stopPointId);
}
