using GoTrack.AlertDefinitions;
using GoTrack.Mobile.AlertDefinitions.DTOs;
using System;
using System.Collections.Generic;

namespace GoTrack.Mobile.AlertDefinitions.JobTimeAlertDefinitions.DTOs;

public class CreateJobTimeAlertDefinitionDto : CreateAlertDefinitionBaseDto
{
    public string Name { get; set; } = string.Empty;
    public TimeOnly StartTime {  get; set; }
    public TimeOnly EndTime { get; set; }
    public List<DayOfWeek> DaysOfWeek { get; set; } = [];
}
