using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Alerts.BaseChecker;
using GoTrack.Alerts.BaseChecker.Models;
using GoTrack.Alerts.CheckZoneInOut.Models;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using Warp10Abstraction;


namespace GoTrack.Alerts.CheckZoneInOut;

public class RepoServiceImpl : IRepoService
{
    private readonly GoTrackAlertCheckZoneInOutContext _dbContext;

    public RepoServiceImpl(GoTrackAlertCheckZoneInOutContext dbContext)
    {
        _dbContext = dbContext;
    }

    public GroupedAlertListBase[] GetGroupedAlertsList(int pageNumber, int pageSize)
    {
        return _dbContext.GroupedAlertLists
            .Skip(pageNumber * pageSize)
            .Take(pageSize)
            .ToArray<GroupedAlertListBase>();
    }

    public int GetGroupedAlertsListCount()
    {
        return _dbContext.GroupedAlertLists.Count();
    }

    public AlertBase[] GetAlertsByIds(string[] ids)
    {
        return _dbContext.AlertLists
            .Where(x => ids.Contains(x.Id.ToString()))
            .ToArray<AlertBase>();
    }

    public AlertBase GetAlert(Guid evId)
    {
        return _dbContext.AlertLists.FirstOrDefault(x => x.Id == evId);
    }

    public void SaveUpdatedAlert()
    {
        _dbContext.SaveChanges();
    }

    public async Task ParseAndSaveAlert(AlertCrudToService alert)
    {
        AlertCrud evCrud = alert.AlertCrud;
        
        var ZoneInOutAlert = (AlertList)GetAlert(evCrud.Id);
        
        if (evCrud.CrudType == CrudType.Update && ZoneInOutAlert is null)
        {
            throw new Exception("Update operation to non existing alert, AlertId: " + evCrud.Id);
        }

        if (evCrud.CrudType == CrudType.Add)
        {
            ZoneInOutAlert = new AlertList()
            {
                Id = evCrud.Id,
                AffectiveFrom = evCrud.AffectiveFrom,
                LastCheckedAt = WarpHelper.ConvertDate(evCrud.AffectiveFrom).ToString(),
            };
        }

        if (evCrud is ZoneInOutAlertCrud zoneInOutAlertCrud)
        {
            ZoneInOutAlert.Imei = zoneInOutAlertCrud.Imei;
            ZoneInOutAlert.Polygon = zoneInOutAlertCrud.Polygon;
        }
        

        if (evCrud.CrudType == CrudType.Add)
        {
            _dbContext.AlertLists.Add(ZoneInOutAlert);
        }

        if (evCrud.CrudType == CrudType.Update)
        {
            _dbContext.AlertLists.Update(ZoneInOutAlert);
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteAlert(AlertBase alertBase)
    {
        _dbContext.AlertLists.Remove((AlertList)alertBase);
        
        await _dbContext.SaveChangesAsync();
    }
}