using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.TrackAccounts;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace GoTrack.Mobile;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class TrackAccountAuthorizeAttribute : Attribute, IAsyncActionFilter
{
    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var trackAccountRepository =
            context.HttpContext.RequestServices.GetRequiredService<IRepository<TrackAccount, Guid>>();
        var currentUser = context.HttpContext.RequestServices.GetRequiredService<ICurrentUser>();
        var currentTrackAccount = context.HttpContext.RequestServices.GetRequiredService<ICurrentTrackAccount>();
        var dataFilter = context.HttpContext.RequestServices.GetRequiredService<IDataFilter>();

        var isAccountAssociateToUser = await trackAccountRepository.AnyAsync(account =>
            account.Id == currentTrackAccount.Id 
            && account.UserTrackAccountAssociations.Any(association => association.UserId == currentUser.Id && association.AssociationType == AssociationType.Owner)
        );
        
        if (!isAccountAssociateToUser)
            throw new EntityNotFoundException(typeof(TrackAccount), currentTrackAccount.Id);

        // TODO we need to validate user access permission to TrackAccount
        var trackAccountId = currentTrackAccount.Id;
        if (trackAccountId is null)
            throw new AggregateException("trackAccountId Can't be Null");


        using (dataFilter.Enable<IHaveTrackAccount>())
        {
            await next();
        }
    }
}