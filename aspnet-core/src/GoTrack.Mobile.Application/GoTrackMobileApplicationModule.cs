using GoTrack.Identity;
using GoTrack.Mobile.Permissions.ManagementProviders;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;
using Volo.Abp.BlobStoring.FileSystem;
using Volo.Abp.BlobStoring;
using Volo.Abp.Data;
using Volo.Abp.FluentValidation;
using Warp10Abstraction.Sensors;
using Warp10Abstraction.WarpLibs;
using Warp10;
using Warp10.WarpLibs;
using Warp10.Sensors;
using GoTrack.TrackAccounts;

namespace GoTrack.Mobile;

[DependsOn(
    typeof(GoTrackDomainModule),
    typeof(AbpAccountApplicationModule),
    typeof(GoTrackMobileApplicationContractsModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
)]
[DependsOn(typeof(AbpBlobStoringFileSystemModule))]
[DependsOn(typeof(AbpBlobStoringModule))]
[DependsOn(typeof(AbpFluentValidationModule))]
    public class GoTrackMobileApplicationModule : AbpModule
{
    private IConfiguration _configuration;

    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        _configuration = context.Services.GetConfiguration();
    }
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddScoped<WarpService>(provider =>
        {
            return new WarpService(
                _configuration["Warp10:Url"],
                _configuration["Warp10:ReadToken"],
                _configuration["Warp10:WriteToken"]
            );
        });
        Configure<AbpAutoMapperOptions>(options => { options.AddMaps<GoTrackMobileApplicationModule>(); });

        //Important: this essentially ensures only CustomerUser are being displayed
        Configure<AbpDataFilterOptions>(options =>
        {
            options.DefaultStates[typeof(ICustomerUserFilter)] = new DataFilterState(isEnabled: true);
            options.DefaultStates[typeof(IHostTenantUserFilter)] = new DataFilterState(isEnabled: false);
            options.DefaultStates[typeof(IHaveTrackAccount)] = new DataFilterState(isEnabled: false);
        });

        context.Services.AddScoped<IWarpLib, WarpLib>();
        context.Services.AddScoped<ISensorRepository, SensorRepository>();
        
        Configure<PermissionManagementOptions>(options =>
        {
            options.ManagementProviders.Add<UserTrackAccountAssociationPermissionManagementProvider>();
        });

        //Configure<AbpPermissionOptions>(options =>
        //{
        //    options.ValueProviders.Add<UserTrackAccountAssociationPermissionValueProvider>();
        //    options.ValueProviders.Add<TrackAccountOwnerPermissionValueProvider>();
        //});
    }
}