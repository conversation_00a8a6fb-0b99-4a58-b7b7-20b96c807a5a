using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests;
using GoTrack.Mobile.Requests.AddVehiclesRequests.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AddVehiclesRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Payments.PromoCodes;
using GoTrack.Settings;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;
using Volo.Abp.Uow;

namespace GoTrack.Mobile.Requests.AddVehiclesRequests;

public class AddVehiclesRequestAppService : GoTrackMobileAppService, IAddVehiclesRequestAppService
{
    private readonly AddVehiclesRequestManager _addVehiclesRequestManager;
    private readonly IRepository<AddVehiclesRequest, Guid> _addVehiclesRequestRepository;
    private readonly ISettingProvider _settingProvider;
    protected IUserFatoraPaymentManager UserFatoraPaymentManager =>
        LazyServiceProvider.LazyGetRequiredService<IUserFatoraPaymentManager>();

    protected BillManager BillManager =>
        LazyServiceProvider.LazyGetRequiredService<BillManager>();

    protected AddVehiclesRequestBillPlanFactory AddVehiclesRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<AddVehiclesRequestBillPlanFactory>();

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();

    protected SubscriptionPlanDefinitionStore SubscriptionPlanDefinitionStore
        => LazyServiceProvider.LazyGetRequiredService<SubscriptionPlanDefinitionStore>();

    public AddVehiclesRequestAppService(
        AddVehiclesRequestManager addVehicleRequestManager,
        IRepository<AddVehiclesRequest, Guid> addVehiclesRequestRepository, ISettingProvider settingProvider)
    {
        _addVehiclesRequestManager = addVehicleRequestManager;
        _addVehiclesRequestRepository = addVehiclesRequestRepository;
        _settingProvider = settingProvider;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public virtual async Task<Guid> CreateAsync(CreateAddVehiclesRequestsDto input)
    {
        var request = await CreateRequestAsync(input.TrackVehicles, TrackerInstallationLocation.OnSite.ToString(), input.HasValidDevice, input.PromoCode);

        return request.Id;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<AddVehiclesRequestDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _addVehiclesRequestRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var addVehiclesRequests = await AsyncExecuter.ToListAsync(query);

        var addVehiclesRequestDtos = ObjectMapper.Map<List<AddVehiclesRequest>, List<AddVehiclesRequestDto>>(addVehiclesRequests);

        return new PagedResultDto<AddVehiclesRequestDto>(totalCount, addVehiclesRequestDtos);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<AddVehiclesRequestDto> GetAsync(Guid id)
    {
        var addVehiclesRequest = await _addVehiclesRequestRepository.GetAsync(id);

        return ObjectMapper.Map<AddVehiclesRequest, AddVehiclesRequestDto>(addVehiclesRequest);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<string> CreatePaymentAsync(CreateAddVehiclesRequestsPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.AddVehiclesRequestFatoraPayEnabled,
            GoTrackSettings.AddVehiclesRequestPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        
        var request = await _addVehiclesRequestRepository.GetAsync(input.RequestId);

        if (request.AddVehiclesRequestStage is not AddVehiclesRequestStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await BillManager.GetByRequestIdAsync(request.Id);

        return await UserFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<BillDto> CreateTempBillAsync(CreateAddVehiclesRequestsDto input)
    {
        if (!string.IsNullOrEmpty(input.PromoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(input.PromoCode);
        }
        var trackAccountSubscription = await TrackAccountSubscriptionManager
            .GetCurrentActiveTrackAccountSubscriptionAsync(CurrentTrackAccount.GetId());

        var remainingMonths = await TrackAccountSubscriptionManager.GetRemainingMonths(trackAccountSubscription.Id);

        var subscriptionPlanDefinition = SubscriptionPlanDefinitionStore.Get(trackAccountSubscription.SubscriptionPlanKey);

        var billPlan = await AddVehiclesRequestBillPlanFactory.GenerateBillPlan(
            new AddVehiclesRequestBillPlanInput(
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                input.TrackVehicles.Count(x => x.NeedsTrackingDevice),
                input.TrackVehicles.Count,
                remainingMonths,
                subscriptionPlanDefinition,
                input.PromoCode
            )
        );

        var bill = await BillManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }


    private async Task<AddVehiclesRequest> CreateRequestAsync(
        List<SubscriptionVehicleInfoCreateDto> trackVehicles,
        string trackerInstallationLocation,
        bool hasValidDevice,
        string? promoCode = null)
    {
        var subscriptionVehicleInfos = trackVehicles
            .Select(dto => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(dto.LicensePlateSubClass, dto.LicensePlateSerial),
                ColorHelper.TryGetColor(dto.Color),
                dto.ConsumptionRate,
                dto.NeedsTrackingDevice))
            .ToList();

        var request = await _addVehiclesRequestManager.CreateAsync(
            CurrentUser.Id!.Value,
            EnumHelper.GetEnumValueByName<TrackerInstallationLocation>(trackerInstallationLocation),
            subscriptionVehicleInfos,
            CurrentTrackAccount.GetId(),
            hasValidDevice,
            promoCode
        );
        
        return request;
    }

}