using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.Mobile.Addresses;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PromoCodes;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Settings;
using GoTrack.SubscriptionPlans;
using GoTrack.Vehicles.LicensePlates;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;
using Volo.Abp.Settings;
using Volo.Abp.Uow;
using Volo.Abp.Validation;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

//[RequiresFeature(GoTrackFeatureDefinitions.SubscriptionRequestManagement)]
public class BusinessAccountSubscriptionRequestAppService : GoTrackMobileAppService,
    IBusinessAccountSubscriptionRequestAppService
{
    private readonly IRepository<BusinessAccountSubscriptionRequest, Guid>
        _businessAccountSubscriptionRequestRepository;

    private readonly IRepository<RequestNote, Guid> _requestNoteRepository;
    private readonly IAddressManager _addressManager;
    private readonly IUserFatoraPaymentManager _userFatoraPaymentManager;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly IBusinessAccountSubscriptionRequestManager _subscriptionRequestManager;
    private readonly BillManager _billManager;
    private readonly BusinessAccountSubscriptionRequestBillPlanFactory _billPlanFactory;
    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();
    
    protected SubscriptionPlanDefinitionStore SubscriptionPlanDefinitionStore 
        => LazyServiceProvider.LazyGetRequiredService<SubscriptionPlanDefinitionStore>();

    private readonly ISettingProvider _settingProvider;

    
    public BusinessAccountSubscriptionRequestAppService(
        IRepository<BusinessAccountSubscriptionRequest, Guid> businessAccountSubscriptionRequestRepository,
        IAddressManager addressManager,
        IRepository<RequestNote, Guid> requestNoteRepository,
        IUserFatoraPaymentManager userFatoraPaymentManager,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository,
        IBusinessAccountSubscriptionRequestManager subscriptionRequestManager, BillManager billManager,
        BusinessAccountSubscriptionRequestBillPlanFactory billPlanFactory, ISettingProvider settingProvider)
    {
        _businessAccountSubscriptionRequestRepository = businessAccountSubscriptionRequestRepository;
        _addressManager = addressManager;
        _requestNoteRepository = requestNoteRepository;
        _userFatoraPaymentManager = userFatoraPaymentManager;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _subscriptionRequestManager = subscriptionRequestManager;
        _billManager = billManager;
        _billPlanFactory = billPlanFactory;
        _settingProvider = settingProvider;
    }

    [Authorize]
    public async Task<BusinessAccountSubscriptionRequestDetailsDto> GetAsync(Guid id)
    {
        var subscriptionRequest = await _businessAccountSubscriptionRequestRepository.GetAsync(id);

        var requestDetailsDto =
            ObjectMapper.Map<BusinessAccountSubscriptionRequest, BusinessAccountSubscriptionRequestDetailsDto>(
                subscriptionRequest);

        var requestNoteQ = (await _requestNoteRepository.GetQueryableAsync())
            .OrderByDescending(note => note.CreationTime)
            .Where(note => note.RequestId == id);

        requestNoteQ = requestNoteQ.Take(100);

        var requestNotes = await AsyncExecuter.ToListAsync(requestNoteQ);

        requestDetailsDto.RequestNotes = ObjectMapper.Map<List<RequestNote>, List<RequestNoteDto>>(requestNotes);

        var userFatoraPaymentQuery = await _userFatoraPaymentRepository.GetQueryableAsync();

        userFatoraPaymentQuery = userFatoraPaymentQuery
            .Where(x => x.Id == id && x.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(x => x.CreationTime);

        var userFatoraPayment = await AsyncExecuter.FirstOrDefaultAsync(userFatoraPaymentQuery);

        requestDetailsDto.PaymentUrl = userFatoraPayment?.Url;
        requestDetailsDto.Price = 10_000;

        return requestDetailsDto;
    }

    [Authorize]
    public virtual async Task<Guid> CreateAsync(BusinessAccountSubscriptionRequestCreateDto createDto)
    {
        return await CreateReqeustAsync(
            createDto.CompanyName,
            createDto.Address,
            createDto.AccountName,
            TrackerInstallationLocation.OnSite,
            createDto.SubscriptionVehicleInfoCreateDtos,
            createDto.SubscriptionPlanKey,
            createDto.UserCount,
            createDto.SmsBundleId,
            createDto.SubscriptionDurationInMonths,
            createDto.PromoCode
        );
    }

    [Authorize]
    public async Task<BillDto> CreateTempBillAsync(BusinessAccountSubscriptionRequestCreateDto createDto)
    {
        if (!string.IsNullOrEmpty(createDto.PromoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(createDto.PromoCode);
        }
        
        var subscriptionVehicleInfos = createDto.SubscriptionVehicleInfoCreateDtos
            .Select(dto => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(dto.LicensePlateSubClass, dto.LicensePlateSerial),
                TryGetColor(dto.Color),
                dto.ConsumptionRate,
                dto.NeedsTrackingDevice))
            .ToList();

        var subscriptionPlanDefinition = await SubscriptionPlanDefinitionStore.GetWithValidate(createDto.SubscriptionPlanKey);

        var billPlan = await _billPlanFactory.GenerateBillPlan(
            new BusinessAccountSubscriptionRequestBillPlanInput
            (
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                subscriptionPlanDefinition,
                createDto.SubscriptionDurationInMonths,
                subscriptionVehicleInfos.Count(x => x.NeedsTrackingDevice),
                subscriptionVehicleInfos.Count(),
                createDto.SmsBundleId,
                createDto.PromoCode
            ));

        var bill = await _billManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }

    [Authorize]
    public async Task<BillDto> CreateTempBillWithoutVehiclesDataAsync(
        BusinessAccountSubscriptionRequestCreateWithoutVehiclesDataDto createDataDto)
    {
        if (!string.IsNullOrEmpty(createDataDto.PromoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(createDataDto.PromoCode);
        }
        
        var subscriptionPlanDefinition = await SubscriptionPlanDefinitionStore.GetWithValidate(createDataDto.SubscriptionPlanKey);

        var billPlan = await _billPlanFactory.GenerateBillPlan(
            new BusinessAccountSubscriptionRequestBillPlanInput
            (
                GuidGenerator.Create(), // Generate a temporary request ID
                CurrentUser.Id!.Value,
                subscriptionPlanDefinition,
                createDataDto.SubscriptionDurationInMonths,
                createDataDto.DeviceCount,
                createDataDto.VehicleCount,
                createDataDto.SmsBundleId,
                createDataDto.PromoCode
            ));

        var bill = await _billManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }

    [Authorize]
    public async Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.BusinessRequestFatoraPayEnabled,
            GoTrackSettings.BusinessRequestPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        
        var request = await _businessAccountSubscriptionRequestRepository.GetAsync(input.RequestId);
        
        if (request.AccountSubscriptionRequestStage is not AccountSubscriptionRequestStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await _billManager.GetByRequestIdAsync(request.Id);

        return await _userFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }

    private async Task<Guid> CreateReqeustAsync(
        string companyName,
        AddressFormDto addressDto,
        string accountName,
        TrackerInstallationLocation trackerInstallationLocation,
        List<SubscriptionVehicleInfoCreateDto> subscriptionVehicleInfoCreateDtos,
        string subscriptionPlanKey,
        int userCount,
        Guid? smsBundleId,
        int subscriptionDurationInMonths,
        string? promoCode = null)
    {
        var address = await _addressManager.CreateAsync(
            addressDto.Street,
            addressDto.Area,
            addressDto.City,
            addressDto.Governorate,
            addressDto.Country
        );

        var subscriptionVehicleInfos = subscriptionVehicleInfoCreateDtos
            .Select(dto => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(dto.LicensePlateSubClass, dto.LicensePlateSerial),
                TryGetColor(dto.Color),
                dto.ConsumptionRate,
                dto.NeedsTrackingDevice))
            .ToList();

        var requestId = await _subscriptionRequestManager.CreateAsync(
            CurrentUser.Id!.Value,
            companyName,
            address,
            accountName,
            trackerInstallationLocation,
            subscriptionVehicleInfos,
            subscriptionPlanKey,
            userCount,
            subscriptionDurationInMonths,
            promoCode,
            smsBundleId
        );
        
        return requestId;
    }

    private static Color TryGetColor(string color)
    {
        try
        {
            return ColorTranslator.FromHtml(color);
        }
        catch (Exception)
        {
            throw new AbpValidationException("", new List<ValidationResult>()
            {
                new("Invalid Color")
            });
        }
    }
}