using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Mobile.SmsBundles.DTOs;
using GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions.DTOs;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.SmsBundles;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions;

[Authorize]
public class TrackAccountSubscriptionAppService : GoTrackMobileAppService, ITrackAccountSubscriptionAppService
{
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    public TrackAccountSubscriptionAppService(
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository,
        IRepository<Request, Guid> requestRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IRepository<SmsBundle, Guid> smsBundleRepository)
    {
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _requestRepository = requestRepository;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _smsBundleRepository = smsBundleRepository;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<TrackAccountSubscriptionDetailDto> GetAsync(Guid id)
    {
        var trackAccountId = CurrentTrackAccount.GetId();
        var subscription = await _trackAccountSubscriptionRepository.GetAsync(x =>
            x.TrackAccountId == trackAccountId &&
            x.Id == id
        );
        return ObjectMapper.Map<TrackAccountSubscription, TrackAccountSubscriptionDetailDto>(subscription);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<TrackAccountSubscriptionDetailDto> GetCurrentSubscriptionAsync()
{
        var trackAccountId = CurrentTrackAccount.GetId();

        var subscription = await TrackAccountSubscriptionManager.GetLastTrackAccountSubscriptionAsync(trackAccountId);
        if (subscription is null)
            throw new EntityNotFoundException(nameof(TrackAccountSubscription));

        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x => x.TrackAccountId == trackAccountId && x.AssociationType == AssociationType.Owner);

        var currentUserCount = await _userTrackAccountAssociationRepository.CountAsync(x => 
            x.TrackAccountId == trackAccountId &&
            x.AssociationType == AssociationType.Observer
        );

        var currentActiveUserCount = await _userTrackAccountAssociationRepository.CountAsync(x =>             
            x.TrackAccountId == trackAccountId &&
            x.AssociationType == AssociationType.Observer &&
            x.Status == UserTrackAccountAssociationStatus.Active
        );
        
        var remainingDurationInMonth = await TrackAccountSubscriptionManager.GetRemainingMonths(subscription.Id);

        var trackAccountSubscriptionDto = ObjectMapper.Map<TrackAccountSubscription, TrackAccountSubscriptionDetailDto>(subscription);
        trackAccountSubscriptionDto.RemainingSubscriptionDurationInMonth = remainingDurationInMonth;

        trackAccountSubscriptionDto.CurrentUserCount = currentUserCount;
        trackAccountSubscriptionDto.CurrentActiveUserCount = currentActiveUserCount;

        List<RequestType> requestTypes = [RequestType.PersonalAccountSubscription, RequestType.BusinessAccountSubscription];
        var request = await _requestRepository.FirstOrDefaultAsync(x => 
            x.OwnerId == userTrackAccountAssociation.UserId &&
            requestTypes.Contains(x.Type)
        );

        if (request is null)
            return trackAccountSubscriptionDto;

        switch (request.Type)
        {
            case RequestType.PersonalAccountSubscription when request is PersonalAccountSubscriptionRequest personalRequest:
            {
                trackAccountSubscriptionDto.TrackerInstallationLocation = personalRequest?.TrackerInstallationLocation.ToString() ?? string.Empty;

                if (personalRequest?.SmsBundleId is null)
                    return trackAccountSubscriptionDto;
                
                using var _ = DataFilter.Disable<ISoftDelete>();

                var smsBundle = await _smsBundleRepository.GetAsync(personalRequest.SmsBundleId.Value);

                trackAccountSubscriptionDto.SmsBundle = ObjectMapper.Map<SmsBundle, SmsBundleDto>(smsBundle);

                break;
            }
            case RequestType.BusinessAccountSubscription when request is BusinessAccountSubscriptionRequest businessRequest:
            {
                trackAccountSubscriptionDto.TrackerInstallationLocation = businessRequest?.TrackerInstallationLocation.ToString() ?? string.Empty;

                if (businessRequest?.SmsBundleId is null)
                    return trackAccountSubscriptionDto;

                using var _ = DataFilter.Disable<ISoftDelete>();

                var smsBundle = await _smsBundleRepository.GetAsync(businessRequest.SmsBundleId.Value);

                trackAccountSubscriptionDto.SmsBundle = ObjectMapper.Map<SmsBundle, SmsBundleDto>(smsBundle);

                break;
            }
        }

        return trackAccountSubscriptionDto;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<TrackAccountSubscriptionDto>> GetListAsync(
        PagedResultRequestDto requestDto)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var query = await _trackAccountSubscriptionRepository.GetQueryableAsync();
        query = query.Where(x => x.TrackAccountId == trackAccountId);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(requestDto);

        var trackAccounts = await AsyncExecuter.ToListAsync(query);

        var trackAccountDtos =
            ObjectMapper.Map<List<TrackAccountSubscription>, List<TrackAccountSubscriptionDto>>(trackAccounts);

        return new PagedResultDto<TrackAccountSubscriptionDto>(totalCount, trackAccountDtos);
    }
}