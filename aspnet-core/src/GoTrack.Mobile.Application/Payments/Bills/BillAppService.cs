using System;
using System.Threading.Tasks;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using GoTrack.SubscriptionPlans;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Localization;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Mobile.Payments.Bills;

[Authorize]
public class BillAppService : GoTrackMobileAppService, IBillAppService
{
    private readonly IRepository<Bill, Guid> _billRepository;
    private readonly BillManager _billManager;

    public BillAppService(
        IRepository<Bill, Guid> billRepository, 
        BillManager billManager)
    {
        _billRepository = billRepository;
        _billManager = billManager;
    }

    public async Task<BillDto> GetBillAsync(Guid requestId)
    {
        var bill = await _billManager.GetByRequestIdAsync(requestId);
        
        return ObjectMapper.Map<Bill, BillDto>(bill);
    }


    
}