using System;
using System.Linq;
using System.Threading.Tasks;
using Fatora.Abstractions;
using Fatora.Abstractions.DTO.CreatePaymentDtos;
using Fatora.Abstractions.DTO.RevesalPaymentDto;
using Fatora.Abstractions.Enums;
using GoTrack.Mobile.Payments.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Requests;
using GoTrack.TrackAccounts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using PaymentStatus = GoTrack.Payments.PaymentStatus;

namespace GoTrack.Mobile.Payments;

public class PaymentAppService : GoTrackMobileAppService, IPaymentAppService
{
    private readonly IIdentityUserRepository _userRepository;
    private readonly IFatoraService _fatoraService;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IUserFatoraPaymentManager _userFatoraPaymentManager;
    public PaymentAppService(
        IFatoraService fatoraService,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository,
        IRepository<Request, Guid> requestRepository,
        IIdentityUserRepository userRepository,
        IUserFatoraPaymentManager userFatoraPaymentManager)
    {
        _fatoraService = fatoraService;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _requestRepository = requestRepository;
        _userRepository = userRepository;
        _userFatoraPaymentManager = userFatoraPaymentManager;
    }

    [Authorize]
    public async Task<string> PayDev(CreatePaymentDto input)
    {
        await _userRepository.GetAsync(CurrentUser.Id!.Value);

        var request = await _requestRepository.GetAsync(input.RequestId);

        var newUserFatoraPayment = new UserFatoraPayment(
            GuidGenerator.Create(),
            CurrentUser.Id!.Value,
            input.RequestId,
            input.Amount,
            input.CallBackUrl,
            null,
            14740084,
            PaymentStatus.Pending,
            request.Type
        );

        await _userFatoraPaymentRepository.InsertAsync(newUserFatoraPayment);

        //TODO: make triggerUrl and put userFatoraPayment.Id in it
        var createPaymentResponseData = await _fatoraService.CreatePaymentAsync(
            new CreatePaymentRequestDto(FatoraLanguage.Arabic, input.Amount, input.CallBackUrl, null, false,
                CurrentUser.Id, null));

        newUserFatoraPayment.SetPaymentIdAndUrlAndTriggerUrl(createPaymentResponseData.PaymentId,
            createPaymentResponseData.Url, "triggerUrl");

        return createPaymentResponseData.Url;
    }

    [AllowAnonymous] // TODO remoteService
    public async Task GetCheckPaymentStatusAsync(Guid userFatoraPaymentId)
    {
        using var _ = DataFilter.Disable<IHaveTrackAccount>();

        await _userFatoraPaymentManager.CheckPaymentStatusAsync(userFatoraPaymentId);
    }

    [AllowAnonymous]
    public async Task GetProcessMultiplePaymentsAsync(Guid masterPaymentId, string userFatoraPaymentIds)
    {
        using var _ = DataFilter.Disable<IHaveTrackAccount>();

        if (string.IsNullOrEmpty(userFatoraPaymentIds))
        {
            Logger.LogError("No payment IDs provided for processing multiple payments");
            return;
        }

        var userFatoraPaymentIdList = userFatoraPaymentIds.Split(',').Select(Guid.Parse).ToList();

        foreach (var userFatoraPaymentId in userFatoraPaymentIdList)
        {
            await GetCheckPaymentStatusAsync(userFatoraPaymentId);
        }
    }

    public async Task ReversalPaymentDevAsync(Guid paymentId)
    {
        //TODO: add try catch
        await _fatoraService.ReversalPayment(new ReversalPaymentRequestDto(FatoraLanguage.English, paymentId));
    }

    [Authorize]
    public async Task<string> PayMultipleRequestsAsync(CreateMultiplePaymentsDto input)
    {
        if (input.RequestIds == null || !input.RequestIds.Any())
        {
            throw new UserFriendlyException(L["NoRequestsSelected"]);
        }

        return await _userFatoraPaymentManager.PayMultipleAsync(
            input.RequestIds,
            CurrentUser.Id!.Value,
            input.Language,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }

    private FatoraLanguage ConvertStringToFatoraLanguage(string language)
    {
        if (Enum.TryParse<FatoraLanguage>(language, true, out var result))
            return result;

        return language.ToLowerInvariant() switch
        {
            "ar" => FatoraLanguage.Arabic,
            "en" => FatoraLanguage.English,
            _ => FatoraLanguage.English,
        };
    }
}