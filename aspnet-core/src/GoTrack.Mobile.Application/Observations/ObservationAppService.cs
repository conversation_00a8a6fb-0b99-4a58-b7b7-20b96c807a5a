using GoTrack.Msisdns;
using GoTrack.Observations.ObservationViewModels;
using GoTrack.Observations;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.AspNetCore.Authorization;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;
using GoTrack.Mobile.Observations.DTOs;
using GoTrack.Mobile.UserTrackAccountAssociations.DTOs;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp.Features;
using Microsoft.Extensions.DependencyInjection;


namespace GoTrack.Mobile.Observations;

public class ObservationAppService : GoTrackMobileAppService, IObservatoinAppService
{
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    private readonly IUserTrackAccountAssociationManager _userTrackAccountAssociationManager;
    private readonly IObservationManager _observationManager;

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.GetRequiredService<TrackAccountSubscriptionManager>();

    protected IMsisdnManager MsisdnManager =>
        LazyServiceProvider.GetRequiredService<IMsisdnManager>();

    protected IObservationReadRepository ObservationReadRepository =>
        LazyServiceProvider.GetRequiredService<IObservationReadRepository>();

    public ObservationAppService(
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository,
        IUserTrackAccountAssociationManager userTrackAccountAssociationManager,
        IObservationManager observationManager)
    {
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _userTrackAccountAssociationManager = userTrackAccountAssociationManager;
        _observationManager = observationManager;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<UserTrackAccountAssociationDto>> GetListAsync(PagedResultRequestDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();

        query = query.Where(x => x.TrackAccountId == trackAccountId && x.UserId != CurrentUser.Id);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input).OrderByDescending(x => x.CreationTime);

        var userTrackAccountAssociations = await AsyncExecuter.ToListAsync(query);

        var userTrackAccountAssociationDtos = ObjectMapper
            .Map<List<UserTrackAccountAssociation>, List<UserTrackAccountAssociationDto>>(userTrackAccountAssociations);

        return new PagedResultDto<UserTrackAccountAssociationDto>(totalCount, userTrackAccountAssociationDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<ObservationViewModelDto>> GetListVehicleAndVehicleGroupOfObseverAsync(
        [Required] Guid userTrackAccountAssociationId, PagedResultRequestDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();

        query = query.Where(x => x.TrackAccountId == trackAccountId && x.Id == userTrackAccountAssociationId);

        var isAssociated = await AsyncExecuter.AnyAsync(query);
        if (!isAssociated)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver]);

        (var totalCount, var observationViewModels) =
            await ObservationReadRepository.GetObserverByUserTrackAccountAssociationIdAsync(
                userTrackAccountAssociationId,
                input.SkipCount,
                input.MaxResultCount
            );

        var observationViewModelDtos = ObjectMapper
            .Map<List<ObservationViewModel>, List<ObservationViewModelDto>>(observationViewModels);

        return new PagedResultDto<ObservationViewModelDto>(totalCount, observationViewModelDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public virtual async Task CreateObserverAsync(CreateObserverDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var trackAccountSubscription = await TrackAccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(trackAccountId);

        var activeObserverCount = await _userTrackAccountAssociationRepository.CountAsync(x =>
            x.TrackAccountId == trackAccountId && 
            x.AssociationType == AssociationType.Observer &&
            x.Status == UserTrackAccountAssociationStatus.Active
        );

        if (activeObserverCount == trackAccountSubscription.UserCount)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.ObserverLimitReached)
                .WithData("MaxObservers", trackAccountSubscription.UserCount);
        }

        var msisdn = MsisdnManager.Create(input.PhoneNumber);

        CheckOwnerAsObservaer(CurrentUser.UserName!, msisdn);

        await CheckObserverOnTrackAccountAsync(trackAccountId, msisdn);

        var userTrackAccountAssociation = await _userTrackAccountAssociationManager.CreateObserverAsync(
            trackAccountId,
            msisdn,
            input.Name
        );

        foreach(var vehicleId in input.VehicleIds)
            await _observationManager.CreateObservationVehicleAsync(userTrackAccountAssociation.Id, vehicleId);

        foreach (var vehicleGroupId in input.VehicleGroupIds)
            await _observationManager.CreateObservationVehicleGroupAsync(userTrackAccountAssociation.Id, vehicleGroupId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public virtual async Task<UserTrackAccountAssociationDto> UpdateObserverAsync(UpdateObserverDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var msisdn = MsisdnManager.Create(input.PhoneNumber);

        await CheckObserverOnTrackAccountAsync(trackAccountId, msisdn);

        var userTrackAccountAssociation = await _userTrackAccountAssociationManager.UpdateAsync(
            input.UserTrackAccountAssociationId,
            input.Name,
            msisdn
        );

        return ObjectMapper.Map<UserTrackAccountAssociation, UserTrackAccountAssociationDto>(userTrackAccountAssociation);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task AddObservationVehicleAsync([Required] Guid userTrackAccountAssociationId,
        [Required] Guid vehicleId)
    {
        await _userTrackAccountAssociationRepository.GetAsync(userTrackAccountAssociationId);

        await _observationManager.AddVehicleAsync(userTrackAccountAssociationId, vehicleId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task RemoveObservationVehicleAsync(
        [Required] Guid userTrackAccountAssociationId,
        [Required] Guid vehicleId)
    {
        await _observationManager.DeleteObservationVehicleAsync(userTrackAccountAssociationId, vehicleId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task AddObservationVehicleGroupAsync(
        [Required] Guid userTrackAccountAssociationId,
        [Required] Guid vehicleGroupId)
    {
        await _userTrackAccountAssociationRepository.GetAsync(userTrackAccountAssociationId);

        await _observationManager.AddVehicleGroupAsync(userTrackAccountAssociationId, vehicleGroupId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task RemoveObservationVehicleGroupAsync(
        [Required] Guid userTrackAccountAssociationId,
        [Required] Guid vehicleGroupId)
    {
        await _observationManager.DeleteObservationVehicleGroupAsync(userTrackAccountAssociationId, vehicleGroupId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task DeleteObserverAsync([Required] string phoneNumber)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var msisdn = MsisdnManager.Create(phoneNumber);

        await _userTrackAccountAssociationManager.DeleteAssociationAsync(msisdn, trackAccountId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task ActivateObserverAsync([Required] Guid userId)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        await _userTrackAccountAssociationManager.ActivateUserAsync(userId, trackAccountId);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ObserverManagement)]
    public async Task DeactivateObserverAsync([Required] Guid userId)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        await _userTrackAccountAssociationManager.DeactivateUserAsync(userId, trackAccountId);
    }

    private void CheckOwnerAsObservaer(string ownerPhoneNumber, Msisdn phoneNumber)
    {
        if (ownerPhoneNumber.Equals(phoneNumber.ToString()))
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.OwnerObserverSameTrackAccount]);
    }

    private async Task CheckObserverOnTrackAccountAsync(Guid trackAccountId, Msisdn phoneNumber)
    {
        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();

        query = query.Where(x => x.TrackAccountId == trackAccountId && x.PhoneNumber == phoneNumber);

        var isExists = await AsyncExecuter.AnyAsync(query);
        if (isExists)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.ObserverAlreadyExists]);
    }
}