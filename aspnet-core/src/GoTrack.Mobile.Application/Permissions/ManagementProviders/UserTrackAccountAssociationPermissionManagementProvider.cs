using GoTrack.Mobile.Permissions.ValueProviders;
using Volo.Abp.Guids;
using Volo.Abp.MultiTenancy;
using Volo.Abp.PermissionManagement;

namespace GoTrack.Mobile.Permissions.ManagementProviders;

public class UserTrackAccountAssociationPermissionManagementProvider : PermissionManagementProvider
{
    public override string Name => UserTrackAccountAssociationPermissionValueProvider.ProviderName;

    public UserTrackAccountAssociationPermissionManagementProvider(IPermissionGrantRepository permissionGrantRepository,
        IGuidGenerator guidGenerator, ICurrentTenant currentTenant)
        : base(
            permissionGrantRepository,
            guidGenerator,
            currentTenant)
    {
    }
}