using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using GoTrack.FCMDevices;
using GoTrack.Identity;
using GoTrack.Mobile.FCMDevices.DTOs;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace GoTrack.Mobile.FCMDevices;

public class FcmDeviceAppService : GoTrackMobileAppService, IFcmDeviceAppService
{
    private readonly FcmDeviceManager _fcmDeviceManager;
    private readonly IRepository<IdentityUserProfile, Guid> _identityUserProfileRepository;
    private readonly IRepository<FcmDevice, Guid> _fcmDeviceRepository;
    public FcmDeviceAppService(
        FcmDeviceManager fcmDeviceManager,
        IRepository<IdentityUserProfile, Guid> identityUserProfileRepository,
        IRepository<FcmDevice, Guid> fcmDeviceRepository)
    {
        _fcmDeviceManager = fcmDeviceManager;
        _identityUserProfileRepository = identityUserProfileRepository;
        _fcmDeviceRepository = fcmDeviceRepository;
    }

    [Authorize]
    public async Task RegisterAsync(FcmDeviceRegisterDto registerDto)
    {
        await _fcmDeviceManager.RegisterDeviceAsync
        (
            registerDto.FcmToken,
            registerDto.DeviceType,
            registerDto.DeviceId,
            CurrentUser.GetId(),
            registerDto.Name
        );
    }

    // [Authorize]
    // public async Task SubmitDeviceTokenAsync([Required] string deviceToken)
    // {
    //     Guid userId = CurrentUser.Id!.Value;
    //     var fcmDevice = await _fcmDeviceRepository.FindAsync(x => x.UserId == userId && x.FcmToken == deviceToken);
    //
    //     if (fcmDevice is null)
    //     {
    //         await _fcmDeviceManager.RegisterDeviceAsync(deviceToken, DeviceType.Android, userId);
    //     }
    //     else
    //     {
    //         //We update LastModificationTime to confirm that the token still active
    //         await _fcmDeviceRepository.UpdateAsync(fcmDevice);
    //     }
    // }

    [Authorize]
    public async Task RemoveDeviceTokenAsync([Required] string deviceToken)
    {
        Guid userId = CurrentUser.Id!.Value;

        var fcmDevice = await _fcmDeviceRepository
            .FindAsync(a => a.UserId == userId && a.FcmToken == deviceToken);

        if (fcmDevice is null)
            throw new BusinessException(GoTrackDomainErrorCodes.DeviceTokenNotFound);

        await _fcmDeviceRepository.DeleteAsync(fcmDevice);
    }

}