using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Payments.Bills;
using GoTrack.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Requests;

public class RequestAppService : GoTrackAppService, IRequestAppService
{
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IRepository<RequestNote, Guid> _requestNoteRepository;
    private readonly RequestManager _requestManager;
    public RequestAppService(IRepository<Request, Guid> requestRepository,
        IRepository<RequestNote, Guid> requestNoteRepository, 
        RequestManager requestManager)
    {
        _requestRepository = requestRepository;
        _requestNoteRepository = requestNoteRepository;
        _requestManager = requestManager;
    }

    [Authorize(GoTrackPermissions.RequestIndex)]
    public async Task<PagedResultDto<RequestDto>> GetAsync(RequestTypePagedResultRequestDto input)
    {
        using (DataFilter.Disable<IHostTenantUserFilter>())
        using (DataFilter.Enable<ICustomerUserFilter>())
        {
            var requestQ =
                await _requestRepository.WithDetailsAsync(request => request.Owner);
            if (input.Type is not null)
            {
                requestQ =
                    requestQ.Where(new RequestTypeSpecification((RequestType)input.Type));
            }

            var count = await AsyncExecuter.CountAsync(requestQ);
            requestQ = requestQ.PageBy(input);
            var requests = await AsyncExecuter.ToListAsync(requestQ);

            var requestDto = new PagedResultDto<RequestDto>(count,
                ObjectMapper.Map<List<Request>, List<RequestDto>>
                    (requests));
            return requestDto;
        }
    }

    [Authorize(GoTrackPermissions.RequestNoteIndex)]
    public async Task<PagedResultDto<RequestNoteDto>> GetNoteAsync(Guid id, PagedResultRequestDto input)
    {
        await _requestRepository.GetAsync(id);
        var requestQ =
            (await _requestNoteRepository.GetQueryableAsync()).OrderByDescending(note => note.CreationTime)
            .Where(note => note.RequestId == id);
        var count = await AsyncExecuter.CountAsync(requestQ);
        requestQ = requestQ.PageBy(input);
        var requestNotes = await AsyncExecuter.ToListAsync(requestQ);
        var requestNoteDto = new PagedResultDto<RequestNoteDto>(count,
            ObjectMapper.Map<List<RequestNote>, List<RequestNoteDto>>
                (requestNotes));
        return requestNoteDto;
    }

    [Authorize(GoTrackPermissions.RequestPayWithCash)]
    public async Task PayWithCashAsync(Guid id, CashPaymentDto input)
    {
        var request = await _requestRepository.GetAsync(id);
        await _requestManager.ProcessPaymentAsync(request,PaymentMethod.Cash,input.Note);
    }
}