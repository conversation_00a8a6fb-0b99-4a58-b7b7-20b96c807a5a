using FluentValidation;
using GoTrack.Devices.DTOs;
using GoTrack.Localization;
using Microsoft.Extensions.Localization;

namespace GoTrack.Devices.Validators;

public class GetDeactivatedDevicesByUserDtoValidator : AbstractValidator<GetDeactivatedDevicesByUserDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public GetDeactivatedDevicesByUserDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.UserId)
            .NotNull()
            .WithName(_localizer["GoTrack:UserId"]);
    }
}
