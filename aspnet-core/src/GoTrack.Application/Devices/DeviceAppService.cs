using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Devices.DTOs;
using GoTrack.Permissions;
using GoTrack.SeedWork;
using GoTrack.TrackAccounts;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Devices;

public class DeviceAppService : GoTrackAppService, IDeviceAppService
{ 
    protected IDeviceManager DeviceManager => LazyServiceProvider.LazyGetRequiredService<IDeviceManager>();

    protected IRepository<Device, Guid> DeviceRepository 
        => LazyServiceProvider.LazyGetRequiredService<IRepository<Device, Guid>>();

    protected IRepository<VehicleDeviceEventLog, Guid> VehicleDeviceEventLogRepository 
        => LazyServiceProvider.LazyGetRequiredService<IRepository<VehicleDeviceEventLog, Guid>>();

    [Authorize(GoTrackPermissions.DeviceDetails)]
    public async Task<DeviceDetailsDto> GetAsync(Guid id)
    {
        var requestQ = await DeviceRepository.WithDetailsAsync(request => request.StatusLogs);
        
        var request = requestQ.SingleOrDefault(subscriptionRequest => subscriptionRequest.Id == id) ??
            throw new EntityNotFoundException(typeof(Device), id);

        return ObjectMapper.Map<Device, DeviceDetailsDto>(request);
    }

    [Authorize(GoTrackPermissions.DeviceCreate)]
    public async Task<Guid> CreateAsync(CreateDeviceDto deviceDto)
    {
        var existingDevice = await DeviceRepository.FirstOrDefaultAsync(device1 => 
            device1.Imei == deviceDto.Imei || 
            device1.Sim == deviceDto.Sim
        );

        if (existingDevice is not null)
        {
            if (existingDevice.Imei == deviceDto.Imei && existingDevice.Sim == deviceDto.Sim)
            {
                throw new BusinessException(GoTrackAdminApplicationErrorCodes.BothImeiAndSimDuplicated);
            }
            else if (existingDevice.Imei == deviceDto.Imei)
            {
                throw new BusinessException(GoTrackAdminApplicationErrorCodes.TheImeiIsDuplicated);
            }
            else if (existingDevice.Sim == deviceDto.Sim)
            {
                throw new BusinessException(GoTrackAdminApplicationErrorCodes.TheSimIsDuplicated);
            }
        }

        var modelDictionary = new Dictionary<string, string>
        {
            { "en", deviceDto.ModelEN },
            { "ar", deviceDto.ModelAr }
        };
        var brandDictionary = new Dictionary<string, string>
        {
            { "en", deviceDto.BrandEN },
            { "ar", deviceDto.BrandAr }
        };

        var model = new LocalizedString(modelDictionary);
        var brand = new LocalizedString(brandDictionary);

        var newDevice = await DeviceManager.CreateAsync(model, brand, deviceDto.Imei, deviceDto.Sim, deviceDto.Protocol);

        return newDevice.Id;
    }

    [Authorize(GoTrackPermissions.DeviceIndex)]
    public async Task<PagedResultDto<DeviceDto>> GetListAsync(GetDeviceListRequestDto input)
    {
        var query = await DeviceRepository.GetQueryableAsync();

        if (!string.IsNullOrWhiteSpace(input.Imei))
        {
            query = query.Where(device => device.Imei.StartsWith(input.Imei));
        }

        if (input.Status.HasValue)
        {
            query = query.Where(device => device.Status == input.Status.Value);
        }

        var count = await AsyncExecuter.CountAsync(query);

        query = query.OrderByDescending(x => x.CreationTime);
        
        query = query.PageBy(input);

        var devices = await AsyncExecuter.ToListAsync(query);

        return new PagedResultDto<DeviceDto>(count, ObjectMapper.Map<List<Device>, List<DeviceDto>>(devices));
    }

    
    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestFinishProcessing)]
    [Authorize(GoTrackPermissions.PersonalAccountSubscriptionRequestFinishProcessing)]
    public async Task<PagedResultDto<AvailableDevicesForInstallationDeviceDto>> GetAvailableDevicesForInstallationAsync(PagedResultRequestDto input)
    {
        var queryable = await DeviceRepository.GetQueryableAsync();

        var devicesForInstallationQuery = queryable.Where(device => device.Status == DeviceStatus.Unconnected && 
                                                                    device.OwnerId == null );

        var count = await AsyncExecuter.CountAsync(devicesForInstallationQuery);
        
        var pagedQuery = devicesForInstallationQuery.PageBy(input);

        var devices = await AsyncExecuter.ToListAsync(pagedQuery);

        return new PagedResultDto<AvailableDevicesForInstallationDeviceDto>(
            count,
            ObjectMapper.Map<List<Device>, List<AvailableDevicesForInstallationDeviceDto>>(devices)
        );
    }

    [Authorize(GoTrackPermissions.GetDeactivatedDevicesByUser)]
    public virtual async Task<PagedResultDto<DeactivatedDevicesDto>> GetDeactivatedDevicesByUserAsync(GetDeactivatedDevicesByUserDto input)
    {
        var query = await DeviceRepository.GetQueryableAsync();

        query = query.Where(device => 
            device.Status == DeviceStatus.ConnectedAndDeactive 
            && device.OwnerId == input.UserId
        );

        var totalCount = await AsyncExecuter.CountAsync(query);

        var pagedQuery = query.PageBy(input);

        var deactivatedDevices = await AsyncExecuter.ToListAsync(pagedQuery);

        var deactivatedDeviceDtos = ObjectMapper.Map<List<Device>, List<DeactivatedDevicesDto>>(deactivatedDevices);

        return new PagedResultDto<DeactivatedDevicesDto>(totalCount, deactivatedDeviceDtos);
    }

    [Authorize(GoTrackPermissions.DeviceTrackAccountInfo)]
    public async Task<DeviceTrackAccountInfoDto> GetTrackAccountInfoAsync(Guid deviceId)
    {
        var device = await DeviceRepository.GetAsync(deviceId);

        if (device.Status is DeviceStatus.Unconnected)
        {
            throw new BusinessException(GoTrackAdminApplicationErrorCodes.DeviceIsUnconnected);
        }
        
        var eventLogQuery = await VehicleDeviceEventLogRepository.GetQueryableAsync();
        
        var deviceTrackAccountInfoDto = await AsyncExecuter.FirstOrDefaultAsync(
            eventLogQuery
                .Where(log => log.DeviceId == deviceId && log.EventName == EventName.Installed)
                .OrderByDescending(log => log.CreationTime)
                .Select(latestInstalledEvent => new DeviceTrackAccountInfoDto
                {
                    TrackAccountId = latestInstalledEvent.Vehicle.TrackAccount.Id,
                    TrackAccountName = latestInstalledEvent.Vehicle.TrackAccount.Name,
                    TrackAccountType = latestInstalledEvent.Vehicle.TrackAccount.AccountType,
                    LinkedVehicle = new LinkedVehicleInfoDto
                    {
                        VehicleId = latestInstalledEvent.Vehicle.Id,
                        LicensePlateSerial = latestInstalledEvent.Vehicle.LicensePlate.Serial,
                        LicensePlateSubClass = latestInstalledEvent.Vehicle.LicensePlate.SubClass,
                        LinkedAt = latestInstalledEvent.CreationTime
                    }
                })
        );

        if (deviceTrackAccountInfoDto is null)
        {
            throw new BusinessException(GoTrackAdminApplicationErrorCodes.DeviceTrackAccountInfoNotFound);
        }
        
        return deviceTrackAccountInfoDto;
    }
}