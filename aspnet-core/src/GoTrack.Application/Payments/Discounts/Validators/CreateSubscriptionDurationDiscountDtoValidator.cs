using FluentValidation;
using GoTrack.Localization;
using GoTrack.Payments.Discounts.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Payments.Discounts.Validators;

public class CreateSubscriptionDurationDiscountDtoValidator : AbstractValidator<CreateSubscriptionDurationDiscountDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public CreateSubscriptionDurationDiscountDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Value)
            .GreaterThan(0)
            .LessThanOrEqualTo(1)
            .When(x => x.IsPercentage)
            .WithName(_localizer["GoTrack:DiscountValue"])
            .WithMessage(_localizer[GoTrackDomainErrorCodes.DiscountPercentageValueOutOfRange]);

        RuleFor(x => x.Value)
            .GreaterThan(0)
            .When(x => x.IsPercentage is false)
            .WithName(_localizer["GoTrack:DiscountValue"])
            .WithMessage(_localizer[GoTrackDomainErrorCodes.DiscountFixedValueMustBePositive]);

        RuleFor(x => x.StartDate)
            .NotNull();

        RuleFor(x => x.DurationInMonth)
            .NotNull()
            .GreaterThan(0)
            .LessThanOrEqualTo(12)
            .WithName(_localizer["GoTrack:DurationInMonth"]);
    }
}