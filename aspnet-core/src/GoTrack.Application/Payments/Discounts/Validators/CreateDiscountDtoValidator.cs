using FluentValidation;
using GoTrack.Localization;
using GoTrack.Payments.Discounts.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Payments.Discounts.Validators;

public class CreateDiscountDtoValidator : AbstractValidator<CreateDiscountDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public CreateDiscountDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Value)
            .NotNull()
            .GreaterThan(0)
            .LessThanOrEqualTo(1)
            .WithName(_localizer["GoTrack:DiscountValue"]);

        RuleFor(x => x.Name)
            .NotNull()
            .NotEmpty();

        RuleFor(x => x.TargetType)
            .NotNull();

        RuleFor(x => x.IsPercentage)
            .NotNull();
        
        RuleFor(x => x.StartDate)
            .NotNull();
    }
}
