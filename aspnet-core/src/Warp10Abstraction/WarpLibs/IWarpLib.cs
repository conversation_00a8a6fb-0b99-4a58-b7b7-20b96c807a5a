using Warp10Abstraction.Models;

namespace Warp10Abstraction.WarpLibs;

public interface IWarpLib
{
    Task<decimal> GetTravelledDistanceAsync(string vehicleImei, List<Tuple<DateTime, DateTime>> listFromTo);
    Task<decimal> GetTravelledDistanceAsync(string vehicleImei, DateTime from, DateTime to);
    Task<decimal> GetTravelledDistanceAsync(string vehicleImei, DateTime dateOnly);
    Task<decimal> GetTravelledDistanceInRoute(string vehicleImei, List<string> hHCodes, DateTime dateOnly);
    Task<decimal> GetTravelledDistanceInRoute(string vehicleImei, List<string> hhCodes, DateTime from, DateTime to);
    Task<ImeiDistanceSpeedStat> GetTravelledDistanceSpeedStats(string imei, DateTime fromDate, DateTime toDate, double ignoreSpeedUnder, int distance = 100_000, bool withIgnition = true);
    Task<string> GetRouteHhCodeAsync(List<WarpCoordinate> coordinates);
    Task<Dictionary<string, WarpGTS[]>> GetLastRecordOfImeisAsync(List<string> imeis, bool onlySpeed);
    Task<List<WarpGTS>> GetDeviceHistoryAsync(string imei, DateTime from, DateTime to, bool onlySpeed);
    Task<ViolationResult[]> CheckImeisOverSpeed(string[] deviceImei, int speedLimit, string fromDate, string toDate);
    Task<ViolationResult[]> CheckImeisInOutArea(string[] deviceImei, string polygon, bool isIn, string fromDate, string toDate);
    Task<ViolationResult> CheckStopInZone(
        string deviceImei,
        int stopped_min_time_seconds,
        int stopped_max_speed_kmh,
        int stopped_max_mean_speed_kmh,
        int stopped_max_radius_meters,
        string polygon,
        string fromDate,
        string toDate
    );
    Task<ViolationResult[]> CheckImeisExternalPowerCutOffAsync(string[] deviceImeis, string fromDate, string toDate);
    Task<string> GetZoneHhCodeAsync(List<WarpCoordinate> coordinates);
}
