using Warp10Abstraction.Models;

namespace Warp10Abstraction;

public class WarpHelper
{
    public static long ConvertDate(DateTime date)
    {
        return ((DateTimeOffset)date).ToUnixTimeMilliseconds();
    }

    public static string GetMultiValuesLabelFilter(string[] imeis, bool checkAllExceptThose = false)
    {
        string labelFilter = "~^(" + (checkAllExceptThose ? "(?!" : "");
        labelFilter += string.Join("|", imeis.Select(x => "^" + x + "$"));
        labelFilter += ")" + (checkAllExceptThose ? ".)*$" : "");
        return labelFilter;
    }

    public static T CheckWarpResponse<T>(WarpResponse<T> response, string script = "")
    {
        if (!response.Result)
            if (response.Exception is not null)
            {
                Console.WriteLine(script);
                throw new WarpException(response.Exception);
            }
            else
                throw new Exception(response.ErrorMessage);
        return response.Data;
    }

    public static IEnumerable<Tuple<DateTime, DateTime>> SplitDateRange(DateTime start, DateTime end, int dayChunkSize = 1)
    {
        DateTime chunkEnd;
        while ((chunkEnd = start.AddDays(dayChunkSize)) < end)
        {
            yield return Tuple.Create(start, chunkEnd);
            start = chunkEnd.AddMilliseconds(1);
        }
        yield return Tuple.Create(start, end);
    }

    public static IEnumerable<Tuple<DateTime, DateTime>> SplitDateRangeIntoConsistantDays(DateTime fromDate, DateTime toDate)
    {
        var splitedDateRange = new List<Tuple<DateTime, DateTime>>();

        if ((toDate - fromDate).Days > 0)
        {
            DateTime nextDay = fromDate.AddDays(1);
            splitedDateRange.Add(Tuple.Create(fromDate, new DateTime(fromDate.Year, fromDate.Month, fromDate.Day, 23, 59, 59)));
            splitedDateRange.AddRange(SplitDateRange(new DateTime(nextDay.Year, nextDay.Month, nextDay.Day, 0, 0, 0), toDate).ToList());
        }
        else
            splitedDateRange = SplitDateRange(fromDate, toDate).ToList();

        return splitedDateRange;
    }

    public static IEnumerable<Tuple<DateTime, DateTime>> SplitDateRangeInHours(DateTime start, DateTime end, int hourChunkSize = 1)
    {
        DateTime chunkEnd;
        while ((chunkEnd = start.AddHours(hourChunkSize)) < end)
        {
            yield return Tuple.Create(start, chunkEnd);
            start = chunkEnd.AddMilliseconds(1);
        }
        yield return Tuple.Create(start, end);
    }

    public static IEnumerable<Tuple<DateTime, DateTime>> SplitDateRangeIntoConsistantHours(DateTime fromDate, DateTime toDate)
    {
        var splitedDateRange = new List<Tuple<DateTime, DateTime>>();

        if ((toDate - fromDate).Hours > 0)
        {
            DateTimeKind fromKind = fromDate.Kind;
            DateTimeKind toKind = toDate.Kind;

            DateTime nextHour = fromDate.AddHours(1);
            splitedDateRange.Add(Tuple.Create(fromDate, new DateTime(nextHour.Year, nextHour.Month, nextHour.Day, nextHour.Hour, 0, 0, toKind)));
            splitedDateRange.AddRange(SplitDateRangeInHours(new DateTime(nextHour.Year, nextHour.Month, nextHour.Day, nextHour.Hour, 0, 0, fromKind), toDate).ToList());
        }
        else
            splitedDateRange = SplitDateRangeInHours(fromDate, toDate).ToList();

        return splitedDateRange;
    }
}