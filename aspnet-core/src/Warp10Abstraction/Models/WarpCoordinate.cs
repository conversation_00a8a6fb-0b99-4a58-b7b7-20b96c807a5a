namespace Warp10Abstraction.Models;

public class WarpCoordinate
{
    public double LatitudeY { get; private set; }

    public double LongitudeX { get; private set; }

    public WarpCoordinate(double longitudeX, double latitudeY)
    {
        if (latitudeY is < -90 or > 90)
            throw new ArgumentOutOfRangeException("Latitude", latitudeY, "Value must be between -90 and 90 inclusive.");

        if (double.IsNaN(latitudeY))
            throw new ArgumentException("Latitude must be a valid number.", "Latitude");

        if (longitudeX is < -180 or > 180)
            throw new ArgumentOutOfRangeException("Longitude", longitudeX, "Value must be between -180 and 180 inclusive.");

        if (double.IsNaN(longitudeX))
            throw new ArgumentException("Longitude must be a valid number.", "Longitude");

        LongitudeX = longitudeX;
        LatitudeY = latitudeY;
    }
}
