using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using NSubstitute;
using NUglify.Helpers;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Timing;
using Xunit;

namespace GoTrack.TrackAccountSubscriptions;

public class TrackAccountSubscriptionManagerTests
{
    private readonly TrackAccountSubscriptionManager _accountSubscriptionManager;
    private readonly IRepository<TrackAccountSubscription, Guid> _subscriptionRepository;
    private readonly DateTime _today = new(2025, 3, 15);
    private readonly DateTime _updateDate = new(2025, 3, 15,13, 0, 0, 0);
    private readonly List<int> _notificationDays = [5,10,15];
    private readonly IClock _clock;
    private readonly SubscriptionPlanDefinitionStore _subscriptionPlanDefinitionStore;

    public TrackAccountSubscriptionManagerTests(SubscriptionPlanDefinitionStore subscriptionPlanDefinitionStore)
    {
        _subscriptionPlanDefinitionStore = subscriptionPlanDefinitionStore;
        _clock = Substitute.For<IClock>();
        _clock.Now.Returns(_ => _today);

        _subscriptionRepository = Substitute.For<IRepository<TrackAccountSubscription, Guid>>();
        _subscriptionRepository.GetQueryableAsync().Returns(new List<TrackAccountSubscription>().AsQueryable());

        _accountSubscriptionManager = new TrackAccountSubscriptionManager(
            null!,
            null!,
            _subscriptionRepository,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!
            );
    }

    private async Task<List<TrackAccountSubscription>> GetSubscriptionsAsync(
        List<TrackAccountSubscription> subscriptions, List<int>? notificationDays = null)
    {
        notificationDays ??= _notificationDays;
        
        _subscriptionRepository.GetQueryableAsync()
            .Returns(subscriptions.AsQueryable());

        foreach (var notificationDay in notificationDays)
        {
            var query = await _accountSubscriptionManager.GetExpiringSubscriptionsQueryAsync(_today, notificationDay);
            var result = query.ToList();

            subscriptions.Where(x => result.Any(y => y.Id == x.Id)).ForEach(x => x.UpdateLastNotificationAt(_updateDate));

            _subscriptionRepository.GetQueryableAsync()
                .Returns(subscriptions.AsQueryable());
        }
        
        return subscriptions.Where(x => x.LastNotificationAt == _updateDate).ToList();
    }


    [Fact]
    public async Task GetSubscriptionToNotify_ShouldReturnEmpty_WhenNoSubscriptionsProvided()
    {
        var result =
            await _accountSubscriptionManager.GetExpiringSubscriptionsQueryAsync(_today, _notificationDays.First());

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldReturnSubscription_WhenExactlyOnNotificationDay()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(15), // Exactly 15 days until expiration
            lastNotificationAt: null // No previous notification
        );

        var result = await GetSubscriptionsAsync([subscription]);
        
        Assert.Single(result);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldSkip_WhenNotificationDayDoesNotMatch()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(12), 
            lastNotificationAt: _today
        );
        
        var result = await GetSubscriptionsAsync([subscription]);
        
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldSkip_WhenSameNotificationAlreadySentToday()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(10), // 10 days until expiration
            lastNotificationAt: _today // Notification already sent today
        );

        var result = await GetSubscriptionsAsync([subscription]);

        Assert.Empty(result);
    }
    

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldInclude_WhenLessUrgentNotificationWasSent()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(5), // 5 days until expiration
            lastNotificationAt: _today.AddDays(-10) // 15-day notification sent 10 days ago
        );

        var result = await GetSubscriptionsAsync([subscription]);

        Assert.Single(result);
        Assert.Same(subscription, result[0]);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldHandleMissedNotifications()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(13), // 13 days until expiration
            lastNotificationAt: null // No previous notification
        );

        var result = await GetSubscriptionsAsync([subscription]);

        Assert.Single(result);
        Assert.Same(subscription, result[0]);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_WhenExpirationDateHasPassed()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(-1), // Already expired
            lastNotificationAt: null
        );

        var result = await GetSubscriptionsAsync([subscription]);

        Assert.Single(result);
    }
    
    [Fact]
    public async Task GetSubscriptionToNotify_ShouldSkip_WhenExpirationDateHasPassed()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(-1), // Already expired
            lastNotificationAt: _today.AddDays(-5)
        );

        var result = await GetSubscriptionsAsync([subscription]);

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldHandleMultipleSubscriptions_WithMixedStates()
    {
        var subscriptions = new List<TrackAccountSubscription>
        {
            // Should be included (10 days, no previous notification)
            CreateSubscription(_today.AddDays(10), null),

            // Should be skipped (12 days, not in notification days)
            CreateSubscription(_today.AddDays(12), null),

            // Should be included (5 days, previous notification was at 15 days)
            CreateSubscription(_today.AddDays(5), _today.AddDays(-10)),

            // Should be skipped (10 days, already notified today)
            CreateSubscription(_today.AddDays(10), _today),

            // Should be skipped (15 days, already notified with 10-day notification)
            CreateSubscription(_today.AddDays(15), _today.AddDays(-5)),

            // Should be included (8 days, catching up missed notification)
            CreateSubscription(_today.AddDays(8), null)
        };

        var result = await GetSubscriptionsAsync(subscriptions,[10]);


        Assert.Equal(3,result.Count()); 
        Assert.Contains(subscriptions[0], result); 
        Assert.Contains(subscriptions[2], result); 
        Assert.Contains(subscriptions[5], result); 
    }

    [Fact]
    public async Task GetSubscriptionToNotify_ShouldHandleEmptyNotificationDaysList()
    {
        var subscription = CreateSubscription(
            expirationDate: _today.AddDays(10),
            lastNotificationAt: null
        );

        var result = await GetSubscriptionsAsync([subscription],[]);
        
        Assert.Empty(result);
    }

    private TrackAccountSubscription CreateSubscription(DateTime expirationDate, DateTime? lastNotificationAt)
    {
        var subscriptionPlanDefinition = _subscriptionPlanDefinitionStore.Get(SubscriptionPlanKeys.Platinum);

        var subscription = new TrackAccountSubscription(
            Guid.NewGuid(),
            Guid.NewGuid(),
            subscriptionPlanDefinition,
            5, // userCount
            10, // smsBundleCount
            6, // subscriptionDurationInMonths
            TrackAccountSubscriptionState.Active,
            _clock
        );

        // Use reflection to set the private properties (alternative to having a test constructor)
        var type = typeof(TrackAccountSubscription);

        // Set expiration date
        var toProperty = type.GetProperty("To");
        toProperty?.SetValue(subscription, expirationDate);

        // Set last notification date if provided
        if (lastNotificationAt.HasValue)
        {
            var lastNotificationProperty = type.GetProperty("LastNotificationAt");
            lastNotificationProperty?.SetValue(subscription, lastNotificationAt);
        }

        return subscription;
    }
}